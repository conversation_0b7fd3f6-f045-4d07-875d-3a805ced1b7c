# 能源管理 API 接口封装使用指南

## 概述

本项目已完成能源管理相关 API 接口的封装，包括设备概览、设备状态列表等功能。所有接口都已配置好 Authorization 头部认证，并提供了完整的 TypeScript 类型支持。

## 配置说明

### 1. 域名配置

在 `public/config.js` 中已配置：

```javascript
window.apiHost = "http://www.jkycloud.top:8880/prod-api";
window.energyApiHost = "http://www.jkycloud.top:8880/prod-api";
```

### 2. 请求拦截器

在 `src/utils/request.ts` 中已配置自动添加 Authorization 头部：

```typescript
config.headers!.Authorization = `Bearer ${token}`;
```

## 主要接口

### 1. 设备概览接口

**接口地址**: `POST /shibomameng/energyManagement/device_Overview`

**使用方法**:

```typescript
import { getDeviceOverview } from "@/api/energy";

// 基础调用
const response = await getDeviceOverview();

// 带参数调用
const response = await getDeviceOverview({
  buildingId: "building_001",
  floorId: "floor_001",
  deviceType: "air_conditioner",
  status: "online",
});
```

### 2. 设备状态列表接口

**接口地址**: `POST /shibomameng/energyManagement/device_statuslist`

**使用方法**:

```typescript
import { getDeviceStatusList } from "@/api/energy";

// 分页查询
const response = await getDeviceStatusList({
  page: 1,
  pageSize: 20,
  buildingId: "building_001",
  status: "normal",
  keyword: "电梯",
});
```

## 类型定义

### 请求参数类型

```typescript
// 设备概览参数
interface DeviceOverviewParams {
  buildingId?: string | number;
  floorId?: string | number;
  deviceType?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
}

// 设备状态列表参数
interface DeviceStatusListParams {
  page?: number;
  pageSize?: number;
  buildingId?: string | number;
  floorId?: string | number;
  deviceType?: string;
  status?: string;
  keyword?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
```

### 响应数据类型

```typescript
// API通用响应结构
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 设备概览响应
interface DeviceOverviewResponse {
  total: number;
  online: number;
  offline: number;
  fault: number;
  maintenance: number;
  deviceList: DeviceOverviewItem[];
}
```

## 在 Vue 组件中使用

### 1. 基础使用

```vue
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getDeviceOverview, getDeviceStatusList } from "@/api/energy";

const deviceData = ref(null);
const loading = ref(false);

const loadData = async () => {
  loading.value = true;
  try {
    const response = await getDeviceOverview();
    if (response.success) {
      deviceData.value = response.data;
    }
  } catch (error) {
    console.error("加载失败:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});
</script>
```

### 2. 使用 Composition API 封装

```typescript
// 在组件中使用
import { useEnergyManagement } from "@/examples/energy-api-usage";

const { deviceOverview, deviceStatusList, loading, loadDeviceOverview, loadDeviceStatusList } =
  useEnergyManagement();
```

## 错误处理

### 1. 基础错误处理

```typescript
try {
  const response = await getDeviceOverview();
  if (response.success) {
    // 处理成功响应
  } else {
    // 处理业务错误
    console.error("业务错误:", response.message);
  }
} catch (error) {
  // 处理网络错误或其他异常
  console.error("请求失败:", error);
}
```

### 2. 重试机制

```typescript
import { fetchWithRetry } from "@/examples/energy-api-usage";

// 使用重试机制
const response = await fetchWithRetry(() => getDeviceOverview(), 3);
```

## 认证说明

所有 API 请求都会自动添加 Authorization 头部，token 从 localStorage 中获取：

```typescript
const token = localStorage.getItem("token");
if (token) {
  config.headers!.Authorization = `Bearer ${token}`;
}
```

确保在调用 API 前已正确设置 token：

```typescript
localStorage.setItem("token", "your-auth-token");
```

## 示例文件

- `src/examples/energy-api-usage.ts` - API 使用示例
- `src/examples/EnergyManagementExample.vue` - Vue 组件示例
- `src/api/energy.ts` - API 接口定义
- `src/models/energy.ts` - 类型定义

## 扩展接口

如需添加新的能源管理接口，请在 `src/api/energy.ts` 中添加：

```typescript
export const newEnergyApi = (params: any) =>
  EnergyRequest.post<ApiResponse<any>>("/shibomameng/energyManagement/new_endpoint", params);
```

同时在 `src/models/energy.ts` 中添加对应的类型定义。
