<template>
  <div class="flex page-container ht100">
    <transition name="left-container">
      <div class="left-container flex flex-column ht100" v-if="pageShow">
        <slot name="leftcontainer" />
      </div>
    </transition>
    <transition name="right-container">
      <div class="right-container flex flex-column ht100" v-if="pageShow">
        <slot name="rightcontainer" />
      </div>
    </transition>

    <!-- 建筑楼层选择器 -->
    <transition name="fade">
      <BuildingFloorSelector />
    </transition>
    <!-- tree -->
    <!-- 是否显示树结构 -->
    <transition name="fade">
      <div v-if="props.showTree" class="tree-container">
        <slot name="treecontainer" />
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { inject, computed, ref } from "vue";
import BuildingFloorSelector from "@/components/BuildingFloorSelector.vue";
import { defineProps } from "vue";

const props = defineProps({
  showTree: {
    type: Boolean,
    default: false,
  },
});

const ips = inject("pageShow", ref<boolean>(true));
const pageShow = computed(() => ips.value);
</script>

<style scoped lang="less">
.page-container {
  justify-content: space-between;
  position: relative;
  img {
    height: 1290px;
    width: 5120px;
    position: absolute;
    z-index: -1;
  }
  .left-container,
  .right-container {
    width: 1300px;
    pointer-events: auto;
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    align-items: center;
  }
  .left-container {
    padding: 24px 0 24px 16px;
  }
  .right-container {
    padding: 24px 16px 24px 0px;
  }
  .tree-container {
    pointer-events: auto;
    position: absolute;
    top: 24px;
    left: 1350px;
    max-height: 600px;
    background: rgba(#001a28, 0.7);
    padding: 24px 10px;
    border: 3px solid;
    border-image: linear-gradient(0deg, #0278faff, #0278fa00) 1;
    overflow: auto;

    // min-width: 500px;
  }
}
</style>
