<template>
  <PageCard :title="'设备状态'">
    <div class="search-box">
      <a-space :size="30">
        <div>
          <span>设备类型：</span>
          <a-select
            v-model:value="status"
            style="width: 120px"
            :options="options1"
            size="large"
            @change="handleChange"
          ></a-select>
        </div>
        <div>
          <span>报警类型：</span>
          <a-select
            v-model:value="build"
            style="width: 120px"
            :options="options2"
            size="large"
            @change="handleChange"
          ></a-select>
        </div>
        <div>
          <span>是否解除：</span>
          <a-select
            v-model:value="floor"
            size="large"
            style="width: 120px"
            :options="options3"
          ></a-select>
        </div>
      </a-space>
      <a-radio-group v-model:value="timer" button-style="solid" size="large" class="time">
        <a-radio-button value="a">今日</a-radio-button>
        <a-radio-button value="b">本周</a-radio-button>
        <a-radio-button value="c">本月</a-radio-button>
      </a-radio-group>
    </div>
    <div class="operation-management-content">
      <div>
        <div class="device-item" v-for="(device, index) in deviceList" :key="index">
          <div class="name">
            <span>{{ device.deviceName }}</span>
            <RightOutlined
              @click="device.isExpand = !device.isExpand"
              :style="{
                display: 'inline-block',
                transition: 'transform 0.25s',
                transform: device.isExpand ? 'rotate(90deg)' : 'rotate(0deg)',
              }"
            />
          </div>
          <div v-if="device.isExpand">
            <table class="custom-table">
              <thead>
                <tr>
                  <th>选择</th>
                  <th>设备编号</th>
                  <th>设备名称</th>
                  <th>运行状态</th>
                  <th>运行频率(Hz)</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in device.factorList" :key="item.key">
                  <td>{{ item.numId }}</td>
                  <td>{{ item.name }}</td>
                  <td>{{ item.address }}</td>
                  <td>{{ item.online }}</td>
                  <td>{{ item.online }}</td>
                  <td>
                    <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
                    <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import { RightOutlined } from "@ant-design/icons-vue";

// import SearchForm from "@/components/SearchForm.vue";
// import BaseTable from "@/components/BaseTable.vue";
// import { ContainerOutlined } from "@ant-design/icons-vue";
import { ref } from "vue";
import type { SelectProps } from "ant-design-vue";
// 定义设备相关的数据类型
// interface Factor {
//   factorName: string;
//   unit: string;
//   value: number;
// }
// interface Device {
//   deviceName: string;
//   factorList: Factor[];
//   isExpand?: boolean;
// }
// const deviceList = ref<Device[]>([]); // 设备数据
const deviceList: any = ref([
  {
    deviceName: "冷源",
    isExpand: false,
    factorList: [
      {
        key: "1",
        number: 1,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "2",
        number: 2,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
    ],
  },
  {
    deviceName: "冷源",
    isExpand: false,
    factorList: [
      {
        key: "1",
        number: 1,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "2",
        number: 2,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
      {
        key: "3",
        number: 3,
        numId: 5446851,
        name: "3层5号表",
        address: "C栋3层",
        online: "离线",
      },
    ],
  },
]);
// const columns = [
//   { title: "表具编号", dataIndex: "numId" },
//   { title: "设备名称", dataIndex: "name" },
//   { title: "位置", dataIndex: "address" },
//   { title: "在线/离线", dataIndex: "online" },
//   { title: "操作", dataIndex: "operation" },
// ];
// const data = [
//   {
//     key: "1",
//     number: 1,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "2",
//     number: 2,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "3",
//     number: 3,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
//   {
//     key: "4",
//     number: 4,
//     numId: 5446851,
//     name: "3层5号表",
//     address: "C栋3层",
//     online: "离线",
//   },
// ];
// const toDetails = (i: any) => {
//   console.log("%c Line:99 🍋 i", "color:#f5ce50", i);
// };

// 表单相关 status

const status = ref("");
const build = ref("");
const floor = ref("");
const timer = ref("a");
const options1 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "在线", value: "online" },
  { label: "离线", value: "offline" },
]);
const options2 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "A栋", value: "A" },
  { label: "b栋", value: "b" },
  { label: "c栋", value: "c" },
  { label: "d栋", value: "d" },
]);
const options3 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "1楼", value: "1" },
  { label: "2楼", value: "2" },
]);
const handleChange = () => {
  console.log("view handleChange");
};
</script>

<style lang="less" scoped>
.page-card {
  height: 644px;
  color: #fff;
  .search-box {
    padding: 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    color: #fff;
    span {
      margin-right: 10px;
    }
  }
  .operation-management-content {
    max-height: 460px;
    padding-right: 16px;
    overflow: auto;
    .name {
      display: flex;
      padding: 21.03px 40px 15.97px 36.54px;
      align-items: center;
      gap: 1038.24px;
      background: #0f2952;
      color: #a2d9ff;
      font-size: 26px;
      margin-top: 10px;
    }
    .custom-table {
      border-collapse: collapse;
      width: 100%;
      background: #000;
      color: #fff;
      font-size: 20px;
      text-align: center;
      border-collapse: separate;
      border-spacing: 0 5px;
      // tr之间有间隔
      tr {
        height: 57px;
        background: #071b33;
      }
    }
    img {
      margin-right: 20px;
      cursor: pointer;
    }
    img:hover {
      opacity: 0.7;
    }

    // 全局滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      width: 6.73px;
      height: 128px;
      flex-shrink: 0;
      border-radius: 4.98px;
      background: #d6d6d633;

      &:hover {
        background: rgba(78, 237, 255, 0.5);
      }
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
}
</style>
