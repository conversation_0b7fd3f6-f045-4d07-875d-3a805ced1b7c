<template>
  <div class="security-perimeter-container">
    <h2>周界防范</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <PerimeterStatus />
        <IntrusionDetection />
      </div>
      <div class="right-panel">
        <AlarmEvents />
        <PatrolRecords />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import PerimeterStatus from "@/components/security/perimeter/PerimeterStatus.vue";
// import IntrusionDetection from "@/components/security/perimeter/IntrusionDetection.vue";
// import AlarmEvents from "@/components/security/perimeter/AlarmEvents.vue";
// import PatrolRecords from "@/components/security/perimeter/PatrolRecords.vue";
</script>

<style scoped lang="less">
.security-perimeter-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
