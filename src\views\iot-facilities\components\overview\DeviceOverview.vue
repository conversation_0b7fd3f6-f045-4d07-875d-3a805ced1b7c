<template>
  <PageCard :title="'设备概览'">
    <div class="shebei-overview">
      <div class="item" v-for="item in [1, 2, 3, 4]" :key="item">
        <div class="item-left">
          <img src="@/assets/pages-icon/iot-facilities/lengreyuan.png" alt="" srcset="" />
          <div class="item-name">冷热源啊</div>
        </div>
        <ul class="item-right">
          <li v-for="i in [1, 2, 3, 4]" :key="i">
            <div>
              <span></span>
            </div>
            <span class="li-name">冷水主机</span>
            <span>
              <span class="li-value">4</span>
              <span class="li-unit">台</span>
            </span>
          </li>
        </ul>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
</script>

<style lang="less" scoped>
.page-card {
  height: 375px;
}
.shebei-overview {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-evenly;
  font-size: 30px;
  color: #fff;
  padding-bottom: 24px;
  padding-top: 32px;
  .item {
    width: 305px;
    // height: 205px;
    flex-shrink: 0;
    display: flex;
    align-items: center;

    .item-left {
      width: 95.4px;
      padding: 16px;
      height: 209px;
      background: #0f2952;
      font-size: 19.21px;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: column;
      img {
        // width: 60px;
        // height: 60px;
      }
      .item-name {
        width: 2em;
        text-align: center;
        // margin-top: 10px;
        // font-size: 20px;
      }
    }
    .item-right {
      font-size: 14px;
      li:nth-child(2n) {
        background: #0f243d;
      }
      li {
        width: 200px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        > span {
          display: flex;
          align-items: center;
        }
        > div {
          width: 18.12px;
          height: 18.12px;
          border-radius: 50%;
          border: 0.7px solid #5ca7ff;
          align-items: center;
          display: flex;
          justify-content: center;
          span {
            width: 11.15px;
            height: 11.15px;
            border-radius: 50%;
            background: #5ca7ff;
          }
        }
        .li-name {
          color: #fff;
          margin-left: -40px;
        }
        .li-value {
          color: #00d0ff;
          font-size: 20px;
          margin-right: 8px;
        }
      }
    }
    // align-items: center;
    // justify-content: space-evenly;
    // background: rgba(11, 41, 77, 1);
    // margin-top: 24px;
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz22 {
  font-size: 22px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
span {
  display: inline-block;
}
</style>
