<template>
  <PageCard :title="'消防事件'">
    <!-- 表格 -->
    <div class="table-container">
      <BaseTable
        class="ant-table-striped"
        :columns="columns"
        :data-source="data"
        :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
        :pagination="false"
        :bordered="false"
      >
        <template v-slot:column="scoped">
          <a-row
            v-if="scoped.column.dataIndex === 'operation'"
            class="operation"
            align="middle"
            justify="center"
          >
            <!-- <a-button type="primary" size="small" @click="toDetails(scoped.record)">
              </a-button> -->
            <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
            <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
          </a-row>
        </template>
      </BaseTable>
    </div>
  </PageCard>
</template>
<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import BaseTable from "@/components/BaseTable.vue";
// import { ref } from "vue";

const columns = [
  { title: "报警时间", dataIndex: "type" },
  { title: "设备名称", dataIndex: "detail" },
  { title: "白净详情", dataIndex: "time" },
  { title: "操作", dataIndex: "operation" },
];

const data = [
  {
    key: "1",
    type: "2023-10-01 12:00:00",
    detail: "设备A",
    time: "正常",
  },
  {
    key: "2",
    type: "2023-10-02 13:00:00",
    detail: "设备B",
    time: "异常",
  },
  {
    key: "3",
    type: "2023-10-03 14:00:00",
    detail: "设备C",
    time: "正常",
  },
];
</script>
<style scoped lang="less">
.page-card {
  width: 704px;
}
.table-container {
  position: relative;
  height: 100%;
  overflow: hidden;
}
:deep .ant-table-striped {
  position: relative;
  left: 10px;
  .table-dan {
    height: 57px;
    margin: 8px;
    td {
      background-color: rgba(7, 27, 51, 1);
      color: #fff;
    }
  }
  .table-shuang {
    height: 57px;
    margin: 8px;
    td {
      background-color: rgba(15, 36, 61, 1);
      color: #fff;
    }
  }
  .ant-table-thead {
    th {
      background-color: rgba(15, 41, 82, 1);
      color: #fff;
      height: 67px;
    }
  }
}

.ant-table-striped :deep(.ant-table-body) {
  overflow-y: auto !important;
  max-height: 590px !important;
}

.ant-table-striped :deep(.ant-table-body::-webkit-scrollbar) {
  position: absolute;
  left: 10px;
  width: 3px;
  height: 8px;
}
img {
  margin-right: 20px;
  cursor: pointer;
}
img:hover {
  opacity: 0.7;
}
</style>
