<template>
  <PageCard :title="'能耗趋势'">
    <div class="qushi">
      <div class="search-box">
        <a-radio-group v-model:value="value1" button-style="solid" size="large" class="name">
          <a-radio-button value="a">电</a-radio-button>
          <a-radio-button value="b">水</a-radio-button>
        </a-radio-group>
        <a-radio-group v-model:value="value2" button-style="solid" size="large" class="time">
          <a-radio-button value="a">今日</a-radio-button>
          <a-radio-button value="b">本月</a-radio-button>
          <a-radio-button value="c">本年</a-radio-button>
        </a-radio-group>
      </div>

      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref } from "vue";

const value1 = ref<string>("a");
const value2 = ref<string>("c");

// const dot_in_chart_base64 =
//   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAErSURBVHgBpZM9TsNAEIXfGiIiIUEq6tCDlCMkoubnEvRQUyAK6jgngCMABwBuQAp6p6aKkJBA/CzvxbPLKsHYEp802rE982zvvAXm8N73GUNG4X94YFwwuqiCDzvWWIdqOqHPhWYud4zeiwduXoHHD8Z7WbS5BOy2gZ2V+L4xY+CcmwaBIZejpy/g5BnQ+hsbGXC+Vq4kp8Cxs/8qdOdwWt2ciuTrwOrs1RhI61TZ7Vt9s1CNftE4kEBPWXKzFu2PsR8Fik80Jmwu6Wb4JxKYKNlabt6ksRpjCVzPBFpojDyRClwp22vH+f6JahJDnWU0wz2TkeaamKSyWTXGiL2TBSvrWp7QWMNktD/brfLTzUDRylHODlPe4DDl6WFaQNZmXNoRDhTW2J+v/wbWctvoQeOrKAAAAABJRU5ErkJggg==";
const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "10%",
    right: "4%",
    top: "8%",
    bottom: "10%",
    // containLabel:true
  },
  xAxis: {
    type: "category",
    // boundaryGap: false,
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLabel: {
      fontSize: 20,
      color: "#FFFFFF",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#FFFFFF",
      },
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: "value",
    name: "单位：kWh",
    nameGap: 30,
    nameTextStyle: {
      fontSize: 20,
      color: "#FFFFFF",
    },
    // max: 200,
    // step: 50,
    // min: 0,
    axisLabel: {
      fontSize: 20,
      color: "#FFFFFF",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#FFFFFF",
      },
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  tooltip: {
    trigger: "axis",
    textStyle: {
      fontSize: 20,
    },
  },
  series: [
    {
      data: [35, 36, 40, 50, 60, 80, 85, 80, 70, 60, 50, 40],
      type: "line",
      itemStyle: {
        color: "#00a0e9",
      },
      lineStyle: {
        width: 3,
        color: "#00a0e9",
      },
      symbolSize: 12,
    },
  ],
});
</script>

<style lang="less" scoped>
.search-box {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  .name,
  .time {
    margin-right: 30px;
    :deep .ant-radio-button-wrapper {
      font-size: 18px;
    }
  }
}
.qushi {
  .chart-container {
    height: 700px;
  }
}
</style>
