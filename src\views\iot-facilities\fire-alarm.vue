<template>
  <div class="fire-alarm-container">
    <h2>消防报警</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <AlarmStatus />
        <FireDetectionSystem />
      </div>
      <div class="right-panel">
        <EmergencyResponse />
        <HistoricalAlarms />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import AlarmStatus from "@/components/iot-facilities/fire-alarm/AlarmStatus.vue";
// import FireDetectionSystem from "@/components/iot-facilities/fire-alarm/FireDetectionSystem.vue";
// import EmergencyResponse from "@/components/iot-facilities/fire-alarm/EmergencyResponse.vue";
// import HistoricalAlarms from "@/components/iot-facilities/fire-alarm/HistoricalAlarms.vue";
</script>

<style scoped lang="less">
.fire-alarm-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
