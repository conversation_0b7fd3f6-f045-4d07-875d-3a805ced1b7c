<template>
  <PageLayout :show-tree="true">
    <template v-slot:leftcontainer>
      <AllTrend />
      <ElectricityStructure />
    </template>
    <template v-slot:centercontainer>
      <!-- 中间内容 -->
    </template>
    <template v-slot:rightcontainer>
      <TenantsElectricity />
      <EnergyConsumptTrends />
    </template>

    <template v-slot:treecontainer>
      <ul class="dong">
        <li
          v-for="item in treeData"
          :key="item.key"
          :class="[item.key === selectedItem ? 'active' : '']"
          @click="selectedItem = item.key"
        >
          {{ item.title }}
        </li>
      </ul>
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import AllTrend from "@/views/energy/components/electricity/AllTrend.vue";
import ElectricityStructure from "@/views/energy/components/electricity/ElectricityStructure.vue";
import TenantsElectricity from "@/views/energy/components/electricity/TenantsElectricity.vue";
import EnergyConsumptTrends from "@/views/energy/components/electricity/EnergyConsumptTrends.vue";

import type { TreeProps } from "ant-design-vue";
import { ref } from "vue";
const treeData: TreeProps["treeData"] = [
  { title: "变电站 1", key: "0-0-1" },
  { title: "变电站 2", key: "0-0-2" },
  { title: "变电站 3", key: "0-0-3" },
  { title: "变电站 4", key: "0-0-4" },
  { title: "变电站 5", key: "0-0-5" },
];
const selectedItem = ref("变电站 1");
</script>
<style scoped lang="less">
.dong {
  li {
    color: #fff;
    font-size: 16px;
    margin-bottom: 5px;
    padding: 8px 16px;
    text-align: center;
    background: #e8e8e84d;
    cursor: pointer;
    pointer-events: auto;
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
    &.active {
      background: #00aabd;
    }
  }
}
</style>
