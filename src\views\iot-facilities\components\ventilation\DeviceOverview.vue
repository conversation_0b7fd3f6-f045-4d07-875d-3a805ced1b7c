<template>
  <PageCard :title="'设备概览'">
    <div class="shebei-overview">
      <div class="item" v-for="item in [1, 2, 3, 4, 5, 6]" :key="item">
        <div class="lou">A楼</div>
        <div class="dian">
          <div>
            <span class="light-blue">90</span>
            <span>/120</span>
          </div>
          <div class="fz22">送风机</div>
        </div>
        <div class="shui">
          <div>
            <span class="light-blue">90</span>
            <span>/120</span>
          </div>
          <div class="fz22">排风机</div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
</script>

<style lang="less" scoped>
.page-card {
  height: 375px;
}
.shebei-overview {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-items: baseline;
  font-size: 30px;
  color: #fff;
  .item {
    width: 400px;
    height: 105px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    background: rgba(11, 41, 77, 1);
    margin-top: 24px;
    .lou {
      position: relative;
      width: 100px;
      text-align: center;
      line-height: 115px;
    }
    .lou::after {
      width: 2px;
      height: 75px;
      position: absolute;
      right: 0px;
      top: 20px;
      content: "";
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0),
        #ccc 48.96%,
        rgba(255, 255, 255, 0)
      );
    }
    .dian,
    .shui {
      width: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      & > div:first-child {
        font-size: 35px;
        font-weight: 700;
        // margin-bottom: 10px;
      }
    }
  }
  // height: 100%;
  .light-blue {
    color: rgba(78, 237, 255, 1);
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz22 {
  font-size: 22px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
</style>
