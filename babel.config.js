module.exports = {
  presets: [
    [
      "@vue/cli-plugin-babel/preset",
      {
        targets: {
          chrome: "59"
        },
        useBuiltIns: "usage",
        corejs: 3
      }
    ],
    [
      "@babel/preset-typescript",
      {
        allExtensions: true,
        isTSX: true
      }
    ]
  ],
  plugins: [
    "@babel/plugin-transform-typescript",
    "@babel/plugin-proposal-optional-chaining",
    "@babel/plugin-proposal-nullish-coalescing-operator",
    "@babel/plugin-transform-arrow-functions",
    "@babel/plugin-transform-destructuring",
    "@babel/plugin-transform-spread"
  ],
};
