/* eslint-disable */
declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
declare interface Window {
  apiHost: any;
  signallingApiHost: any;
  energyApiHost: any;
  socketUrl: any;
  ueeLoad: any;
  ue: any;
  ue5: any;
  uee: any;
  addResponseEventListener?: any;
}

declare module "*.svg" {
  const value: any;
  export default value;
}
declare module "*.png" {
  const value: any;
  export default value;
}
