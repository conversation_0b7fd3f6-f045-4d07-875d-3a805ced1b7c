<template>
  <div class="elevator-operation-container">
    <h2>电梯运行管理</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <OperationalMonitoring />
        <EnergyConsumption />
      </div>
      <div class="right-panel">
        <TrafficAnalysis />
        <AlarmEvents />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import OperationalMonitoring from "@/components/elevator/operation/OperationalMonitoring.vue";
// import EnergyConsumption from "@/components/elevator/operation/EnergyConsumption.vue";
// import TrafficAnalysis from "@/components/elevator/operation/TrafficAnalysis.vue";
// import AlarmEvents from "@/components/elevator/operation/AlarmEvents.vue";
</script>

<style scoped lang="less">
.elevator-operation-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
