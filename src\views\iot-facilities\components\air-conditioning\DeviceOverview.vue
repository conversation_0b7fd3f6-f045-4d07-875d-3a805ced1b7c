<template>
  <PageCard :title="'设备概览'">
    <div class="device-overview">
      <ul>
        <li v-for="item in 7" :key="item">
          <div class="left">
            <img src="@/assets/pages-icon/iot-facilities/lsjz.png" alt="" srcset="" />
            <div class="tips">6</div>
          </div>
          <div class="right">
            <div class="text">冷却水机组</div>
            <div class="num">
              <span class="primary">3</span>
              /
              <span>5</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
</script>

<style lang="less" scoped>
.page-card {
  height: 375px;
  color: #fff;
  .device-overview {
    height: 100%;
    display: flex;
    ul {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      li {
        width: 25%;
        padding: 0 24px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .left {
          width: 110px;
          height: 98px;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .tips {
            position: absolute;
            right: 0;
            top: 0;
            width: 30.19px;
            height: 30.19px;
            background-color: #f00;
            text-align: center;
            border-radius: 50%;
            font-size: 22px;
            line-height: 30.19px;
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          justify-self: space-between;
          // align-items: flex-end;
          margin-left: 16px;
          .text {
            font-size: 25px;
          }
          .num {
            font-size: 38px;
          }
        }
      }
    }
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz22 {
  font-size: 22px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
span {
  display: inline-block;
}
.primary {
  color: #00fff0;
}
</style>
