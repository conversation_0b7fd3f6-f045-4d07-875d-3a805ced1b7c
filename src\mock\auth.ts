// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: "admin",
    password: "123456",
    name: "管理员",
    role: "admin",
    avatar: "",
    permissions: ["*"],
  },
  {
    id: 2,
    username: "user",
    password: "123456",
    name: "普通用户",
    role: "user",
    avatar: "",
    permissions: ["read"],
  },
  {
    id: 3,
    username: "NQI",
    password: "nqi!@#123",
    name: "NQI用户",
    role: "admin",
    avatar: "",
    permissions: ["*"],
  },
];

// 模拟登录接口
export const mockLogin = (username: string, password: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      const user = mockUsers.find((u) => u.username === username && u.password === password);

      if (user) {
        const token = `mock-token-${user.id}-${Date.now()}`;
        resolve({
          code: 200,
          message: "登录成功",
          data: {
            token,
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role,
              avatar: user.avatar,
              permissions: user.permissions,
            },
          },
        });
      } else {
        reject({
          code: 401,
          message: "用户名或密码错误",
        });
      }
    }, 1000);
  });
};

// 模拟获取用户信息接口
export const mockGetUserInfo = (token: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 从token中解析用户ID
      const user = mockUsers[2];
      if (user) {
        resolve({
          code: 200,
          message: "获取用户信息成功",
          data: {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
            permissions: user.permissions,
          },
        });
      } else {
        reject({
          code: 401,
          message: "token无效",
        });
      }
    }, 500);
  });
};

// 模拟退出登录接口
export const mockLogout = (): Promise<any> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: "退出登录成功",
      });
    }, 300);
  });
};
