<template>
  <div class="echarts-container" ref="commonChartDom">
    <div class="echarts" ref="chartDom"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, markRaw } from "vue";
import _ from "lodash";
import { useStore } from "vuex";
import * as echart from "echarts";
const props = defineProps<{
  resizeFlag?: boolean;
  echartObj?: any;
}>();
const commonChartDom = ref(null);
const store = useStore();
const emit = defineEmits<{
  (e: "chartclick", params: object): void;
}>();

const chart: any = ref(null);
const chartDom = ref(null);

defineExpose({
  commonChartDom,
  chart,
});
onMounted(() => {
  setCharts();
});
onUnmounted(() => {
  if (!chart.value) return;
  chart.value.clear();
});
const setCharts = () => {
  if (!chartDom.value) return;
  // 此处 markRaw 会导致echarts入场动画失效，如果要打开入场动画，去除markRaw即可
  // chart.value = markRaw(echart.init(chartDom.value));
  chart.value = markRaw(echart.init(chartDom.value));
  chart.value.on("click", (params: any) => {
    emit("chartclick", params);
  });
  refresh();
};
const refresh = () => {
  if (!chartDom.value || !props.echartObj) return;
  const option = _.cloneDeep(props.echartObj);
  if (_.isArray(option.series)) {
    option.series = option.series.map((item: any) => {
      let val = item;
      val.animationDelay = (idx: number) => {
        return idx * 10 + 100;
      };
      return val;
    });
  }
  chart.value.setOption(
    {
      ...option,
      ...{
        animationEasing: "elasticOut",
        animationDelayUpdate: (idx: number) => {
          return idx * 5;
        },
      },
    },
    true,
  );
};
const screenSize = computed(() => store.state.screenSize);
watch(
  () => screenSize,
  async (val) => {
    if (!val) return;
    if (chart.value) {
      // chart.value.resize();
    }
  },
  { deep: true },
);
watch(
  () => props.echartObj,
  async (val) => {
    if (!val) return;
    refresh();
  },
  { deep: true, immediate: true },
);
watch(
  () => props.resizeFlag,
  async () => {
    setTimeout(() => {
      if (chart.value) {
        // chart.value.resize();
      }
    }, 200);
  },
  { deep: true, immediate: true },
);
</script>
<style scoped>
.echarts,
.echarts-container {
  width: 100%;
  height: 100%;
}
</style>
