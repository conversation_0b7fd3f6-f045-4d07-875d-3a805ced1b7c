<template>
  <div>
    <SearchForm
      :fields="searchFields"
      :initialValues="initialValues"
      :defaultTimeRange="'month'"
      :showSearchBox="true"
      :showTimeRange="false"
      @search="handleSearch"
      @reset="handleReset"
      @timeRangeChange="handleTimeRangeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import SearchForm from "@/components/SearchForm.vue";

const searchFields = ref([
  {
    type: "select" as const,
    prop: "businessType",
    formItem: { label: "业务类型" },
    attrs: {
      placeholder: "请选择",
      clearable: true,
    },
    options: [
      { label: "类型1", value: "1" },
      { label: "类型2", value: "2" },
    ],
    on: {
      change: (val) => {
        console.log("业务类型变化:", val);
      },
      clear: () => {
        console.log("业务类型清空");
      },
    },
  },
  {
    type: "input" as const,
    prop: "keyword",
    formItem: { label: "关键词" },
    attrs: {
      placeholder: "请输入关键词",
      clearable: true,
    },
  },
  {
    type: "select" as const,
    prop: "status",
    formItem: { label: "状态" },
    showRight: true,
    attrs: {
      placeholder: "请选择状态",
      clearable: true,
      size: "large",
    },
    options: [
      { label: "全部", value: "" },
      { label: "启用", value: "1" },
      { label: "禁用", value: "0" },
    ],
    on: {
      change: (val) => {
        console.log("状态变化:", val);
      },
    },
  },
  {
    type: "radio" as const,
    prop: "timeRange",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "日", value: "day" },
      { label: "月", value: "month" },
      { label: "年", value: "year" },
    ],
    on: {
      change: (val) => {
        console.log("时间范围变化:", val);
      },
    },
  },
]);

const initialValues = ref({
  businessType: "1",
  status: "",
  timeRange: "month",
});

const handleSearch = (values) => {
  console.log("搜索参数:", values);
};

const handleReset = () => {
  console.log("重置搜索");
};

const handleTimeRangeChange = (timeRange) => {
  console.log("时间范围变化:", timeRange);
};
</script>
