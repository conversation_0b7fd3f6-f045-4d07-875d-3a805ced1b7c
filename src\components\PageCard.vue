<template>
  <a-layout class="page-card">
    <a-layout-header>
      <a-row class="card-title" @click="specialModel">
        <div class="title">
          <div class="icon">
            <span v-if="iconComponent">
              <component :is="iconComponent" />
            </span>
            <span v-else>
              <img src="@/assets/pages-icon/energy/page_card_icon.png" alt="" srcset="" />
            </span>
          </div>
          <div class="card-text">
            <span>{{ title }}</span>
            <div></div>
          </div>
        </div>
      </a-row>
    </a-layout-header>
    <a-layout-content><slot /></a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
import { computed, inject, VNode } from "vue";
import type { Emitter } from "mitt";
// 注入事件总线
const bus = inject<Emitter<any>>("bus")!;

const props = defineProps<{
  title?: string;
  iconComponent?: VNode;
}>();

const title = computed(() => props.title);
const specialModel = () => {
  switch (title.value) {
    case "安防管理":
      bus.emit("SwitchMenu", "anfang");
      break;
    case "电梯管理":
      bus.emit("SwitchMenu", "dianti");
      break;
    default:
      break;
  }
};
// 写一个千分位转换的函数
// 使用moment.js
// onMounted(async () => {

// });
</script>
<style scoped lang="less">
.page-card {
  width: 100%;
  max-width: 1300px;
  box-sizing: border-box;
  padding: 16px;
  border: 3px solid;
  border-image: linear-gradient(0deg, #0278faff, #0278fa00) 1;
  background: rgba(0, 26, 40, 0.7);
  flex: none; // 阻止ant-layout自动伸缩
  // border-radius: 12px;
  .card-title {
    .title {
      font-family: "HarmonyOS Sans SC";
      font-style: normal;
      font-weight: 700;
      width: 1300px;
      font-size: 40px;
      display: flex;
      align-items: center;
      line-height: 40px;
      color: var(--text-color);
      text-shadow: 0px 4px 4px var(--shadow-color);
      position: relative;
      .icon {
        width: 48px;
        height: 50px;
        font-size: 30px;
        margin-right: 10px;
        color: rgba(92, 167, 255, 1);
        img {
          width: 100%;
          height: 100%;
        }
      }
      .card-text {
        flex: 1;
        font-size: 30px;
        display: flex;
        flex-flow: column;
        align-items: flex-start;
        justify-content: center;
        div {
          width: 100%;
          height: 4px;
          background: linear-gradient(
            to right,
            #409eff 0%,
            #409eff 10%,
            #666 10%,
            #666 95%,
            #409eff 95%,
            #409eff 100%
          );
          border-radius: 2px;
          margin-top: 4px;
        }
      }
    }
  }
  .ant-layout-header {
    height: auto;
    padding: 0;
    background: transparent;
  }
}
</style>
