<template>
  <PageCard :title="'消防预案'">
    <div class="plan-img">
      <img src="@/assets/pages-icon/iot-facilities/geipaishui.png" alt="" />
    </div>
    <div class="plan-text">
      <span>演练内容</span>
      <span>消防演练待补充</span>
    </div>
  </PageCard>
</template>
<script setup>
import PageCard from "@/components/PageCard.vue";
</script>
<style lang="less" scoped>
.page-card {
  width: 552px;
}
.plan-img {
  width: 498.88px;
  padding-top: 16px;
  img {
    width: 100%;
    height: 251px;
  }
  // background: url("@/assets/pages-icon/iot-facilities/geipaishui.png") lightgray 50% / cover
  //   no-repeat;
}
.plan-text {
  color: #a2d9ff;
  display: inline-flex;
  padding: 14.34px 134.02px 15.66px 35.13px;
  align-items: flex-start;
  gap: 82.85px;
  background: #071b33;
  font-size: 22.94px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 24px;
}
</style>
