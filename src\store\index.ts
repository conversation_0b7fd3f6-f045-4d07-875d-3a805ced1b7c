import { createStore } from "vuex";
import { globalState, screenSizeModel } from "@/models/global";
import menuIcon from "@/utils/menuIcon";
export default createStore({
  state: {
    // menuList: [
    //   {
    //     routeName: "/wlgz",
    //     iconName: "wlgz",
    //     title: "楼宇物联",
    //     submenuList: [
    //       {
    //         btnName: "安全",
    //         key: "secert",
    //         bimId: "anfang",
    //         routeName: "/wlgz",
    //       },
    //       {
    //         btnName: "物联",
    //         key: "lot",
    //         bimId: "wulian",
    //         routeName: "/wlgzwl",
    //       },
    //     ],
    //   },
    //   {
    //     routeName: "/rxyd",
    //     iconName: "rxyd",
    //     title: "光热系统",
    //     bimId: "guangre",
    //   },
    //   {
    //     routeName: "/szth",
    //     iconName: "szth",
    //     title: "楼宇碳汇",
    //     bimId: "tanhui",
    //   },
    //   {
    //     routeName: "/hjjk",
    //     iconName: "hjjk",
    //     title: "室内健康",
    //     bimId: "huanjing",
    //   },
    //   {
    //     routeName: "/jlzm",
    //     iconName: "jlzm",
    //     title: "节律照明",
    //     bimId: "zhaoming",
    //   },
    //   {
    //     routeName: "/yjdd",
    //     iconName: "yjdd",
    //     title: "应急调度",
    //     bimId: "yingji",
    //   },
    //   {
    //     routeName: "/zhhy",
    //     iconName: "zhhy",
    //     title: "智慧会议",
    //     bimId: "huiyi",
    //   },
    //   {
    //     routeName: "/kjzt",
    //     iconName: "kjzt",
    //     title: "科技展厅",
    //     bimId: "zhanting",
    //   },
    // ],
    menuList: [
      {
        routeName: "/iot-facilities",
        iconName: "iot",
        icon: menuIcon.icon1,
        checkIcon: menuIcon.checkicon1,
        title: "物联设施",
        submenuList: [
          {
            btnName: "总览",
            key: "overview",
            routeName: "/iot-facilities/overview",
          },
          {
            btnName: "空调系统",
            key: "air-conditioning",
            routeName: "/iot-facilities/air-conditioning",
          },
          {
            btnName: "送排风",
            key: "ventilation",
            routeName: "/iot-facilities/ventilation",
          },
          {
            btnName: "给排水",
            key: "water-supply",
            routeName: "/iot-facilities/water-supply",
          },
          {
            btnName: "消防报警",
            key: "fire-alarm",
            routeName: "/iot-facilities/fire-alarm",
          },
        ],
      },
      {
        routeName: "/security",
        iconName: "security",
        title: "安防设备",
        icon: menuIcon.icon2,
        checkIcon: menuIcon.checkicon2,
        submenuList: [
          {
            btnName: "总览",
            key: "overview",
            routeName: "/security/overview",
          },
          {
            btnName: "视频监控",
            key: "video",
            routeName: "/security/video",
          },
          {
            btnName: "门禁考勤",
            key: "access",
            routeName: "/security/access",
          },
          {
            btnName: "周界防范",
            key: "perimeter",
            routeName: "/security/perimeter",
          },
          {
            btnName: "停车场管理",
            key: "parking",
            routeName: "/security/parking",
          },
        ],
      },
      {
        routeName: "/energy",
        iconName: "energy",
        title: "能源管理",
        icon: menuIcon.icon3,
        checkIcon: menuIcon.checkicon3,
        submenuList: [
          {
            btnName: "总览",
            key: "overview",
            routeName: "/energy/overview",
          },
          {
            btnName: "电",
            key: "electricity",
            routeName: "/energy/electricity",
          },
          {
            btnName: "水",
            key: "water",
            routeName: "/energy/water",
          },
        ],
      },
      {
        routeName: "/parking",
        iconName: "parking",
        title: "停车管理",
        icon: menuIcon.icon4,
        checkIcon: menuIcon.checkicon4,
        submenuList: [
          {
            btnName: "总览",
            key: "overview",
            routeName: "/parking/overview",
          },
          {
            btnName: "车位管理",
            key: "space",
            routeName: "/parking/space",
          },
          {
            btnName: "停车费用管理",
            key: "fee",
            routeName: "/parking/fee",
          },
        ],
      },
      {
        routeName: "/elevator",
        iconName: "elevator",
        title: "电梯管理",
        icon: menuIcon.icon5,
        checkIcon: menuIcon.checkicon5,
        submenuList: [
          {
            btnName: "总览",
            key: "overview",
            routeName: "/elevator/overview",
          },
          {
            btnName: "电梯运行管理",
            key: "operation",
            routeName: "/elevator/operation",
          },
          {
            btnName: "电梯维保管理",
            key: "maintenance",
            routeName: "/elevator/maintenance",
          },
        ],
      },
    ],

    screenSize: {
      width: document.documentElement.clientWidth, // 屏幕宽度
      height: document.documentElement.clientHeight, // 屏幕高度
      wideScreen: document.documentElement.clientWidth > 1400, // 宽屏模式
      update: false, // 用于内容模式下的echart更新问题
    },
    mapVisible: true,
    webRtcVisible: false,
    showExitBtn: false,
    theme: "white",
    // theme: "dark",
    subMenuCheckedKey: "",
    currentFloor: "",
    showVideoDigtal: false,
    overMenuList: [
      {
        routeName: "/pwlgz",
        iconName: "pwlgz",
        title: "园区感知",
      },
      {
        routeName: "/pzhtc",
        iconName: "pzhtc",
        title: "停车服务",
        bimId: "tingche",
      },
      {
        routeName: "/prxyd",
        iconName: "rxyd",
        title: "柔性用电",
      },
      {
        routeName: "/pszth",
        iconName: "pszth",
        title: "园区碳汇",
        bimId: "tanhui",
      },
      {
        routeName: "/phjjk",
        iconName: "hjjk",
        title: "生态环境",
        bimId: "huanjing",
      },
      {
        routeName: "/pzhct",
        iconName: "pzhct",
        title: "智慧餐厅",
        bimId: "canting",
      },
    ],
    userInfo: null,
    isLoggedIn: !!localStorage.getItem("token"),
  },
  getters: {},
  mutations: {
    initSreenSize(state: globalState, val: screenSizeModel) {
      // 变更状态
      state.screenSize = val;
    },
    initMapVisible(state: globalState, val: boolean) {
      // 变更状态
      state.mapVisible = val;
    },
    initWebRtcVisible(state: globalState, val: boolean) {
      // 变更状态
      state.webRtcVisible = val;
    },
    initShowExitBtn(state: globalState, val: boolean) {
      // 变更状态
      state.showExitBtn = val;
    },
    changeTheme(state: globalState, val: string) {
      // 切换主题
      state.theme = val;
    },
    changeFloor(state: globalState, val: string) {
      // 切换楼层
      state.currentFloor = val;
    },
    changeVideoDigtalStatus(state: globalState, val: boolean) {
      // 弹出框的显示和隐藏
      state.showVideoDigtal = val;
    },
    setLoginStatus(state: any, status: boolean) {
      state.isLoggedIn = status;
    },
    setUserInfo(state: any, userInfo: any) {
      state.userInfo = userInfo;
    },
    logout(state: any) {
      state.isLoggedIn = false;
      state.userInfo = null;
      localStorage.removeItem("token");
    },
  },
  actions: {},
  modules: {},
});
