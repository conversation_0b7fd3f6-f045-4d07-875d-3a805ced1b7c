<template>
  <div class="security-overview-container">
    <h2>安防设备总览</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <SecurityDeviceStatus />
        <AlarmStatistics />
      </div>
      <div class="right-panel">
        <SecurityMap />
        <RecentEvents />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import SecurityDeviceStatus from "@/components/security/overview/SecurityDeviceStatus.vue";
// import AlarmStatistics from "@/components/security/overview/AlarmStatistics.vue";
// import SecurityMap from "@/components/security/overview/SecurityMap.vue";
// import RecentEvents from "@/components/security/overview/RecentEvents.vue";
</script>

<style scoped lang="less">
.security-overview-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
