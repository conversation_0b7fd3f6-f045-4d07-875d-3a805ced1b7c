<template>
  <PageCard class="page-card" title="园区安防">
    <div class="security-container">
      <div class="security-images">
        <div class="image-grid">
          <div class="image-item large">
            <!-- <img src="/example/anfang_video_1.png" alt="安防监控1" /> -->
            <div class="image-overlay">
              <span>主入口监控</span>
            </div>
          </div>
          <div class="image-item">
            <!-- <img src="/example/anfang_video_2.png" alt="安防监控2" /> -->
            <div class="image-overlay">
              <span>停车场监控</span>
            </div>
          </div>
          <div class="image-item">
            <!-- <img src="/example/anfang_video_3.png" alt="安防监控3" /> -->
            <div class="image-overlay">
              <span>楼层监控</span>
            </div>
          </div>
          <div class="image-item">
            <!-- <img src="/example/anfang_video_4.png" alt="安防监控4" /> -->
            <div class="image-overlay">
              <span>周界监控</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
// import { ref } from "vue";
import PageCard from "@/components/PageCard.vue";

// const securityData = ref({
//   cameras: 156,
//   accessPoints: 24,
//   alarms: 48,
//   patrols: 12,
// });
</script>

<style scoped lang="less">
.page-card {
  height: 425px;
}
.security-container {
  padding: 20px;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  // gap: 10px;
  .security-images {
    // flex: 1;
    .image-grid {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 15px 30px;
      .image-item {
        position: relative;
        border-radius: 8px;
        // overflow: hidden;
        border: 2px solid rgba(78, 237, 255, 0.2);

        &.large {
          grid-row: span 2;
          img {
            width: 600px;
            height: 320px;
          }
        }

        img {
          width: 300px;
          height: 150px;
          // object-fit: cover;
        }

        .image-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
          padding: 15px;

          span {
            font-family: "HarmonyOS Sans SC";
            font-size: 24px;
            color: #ffffff;
            font-weight: 500;
          }
        }

        &:hover {
          border-color: rgba(78, 237, 255, 0.6);

          .image-overlay {
            background: linear-gradient(transparent, rgba(78, 237, 255, 0.3));
          }
        }
      }
    }
  }
}
</style>
