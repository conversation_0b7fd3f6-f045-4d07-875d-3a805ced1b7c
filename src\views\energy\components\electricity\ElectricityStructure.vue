<template>
  <PageCard :title="'用电结构'">
    <div class="search-box">
      <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
        <a-radio-button value="day">今日</a-radio-button>
        <a-radio-button value="month">本月</a-radio-button>
        <a-radio-button value="year">本年</a-radio-button>
      </a-radio-group>
    </div>

    <div class="chart-container">
      <div class="chart-left">
        <common-chart :echart-obj="typeChartOption" />
      </div>
      <div class="chart-right">
        <common-chart :echart-obj="detailChartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref, reactive } from "vue";

const timeRange = ref("year");

// 用电类型饼图配置
const typeChartOption = reactive({
  backgroundColor: "transparent",
  tooltip: {
    trigger: "item",
    formatter: "{b}: {c}%",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
    },
  },
  series: [
    {
      name: "用电类型",
      type: "pie",
      emphasis: {
        scaleSize: 20, // 高亮后扇区的放大尺寸
      },
      radius: ["45%", "90%"],
      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      itemStyle: {
        // borderRadius: 10,
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 2,
      },
      label: {
        show: true,
        position: "outside",
        formatter: "{b}\n{c}%",
        color: "#fff",
        fontSize: 16,
        fontWeight: "bold",
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10,
        lineStyle: {
          color: "#fff",
        },
      },
      data: [
        {
          value: 45,
          name: "照明",
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: "#00c6ff" },
                { offset: 0.5, color: "#fff" },
                { offset: 1, color: "#0072ff" },
              ],
            },
          },
        },
        {
          value: 30,
          name: "空调",
          itemStyle: { color: "#9f3bff" },
        },
        {
          value: 15,
          name: "动力",
          itemStyle: { color: "#00ff9d" },
        },
        {
          value: 10,
          name: "其他",
          itemStyle: { color: "#ffb545" },
        },
      ],
    },
    {
      name: "中心文字",
      type: "pie",
      radius: ["0", "30%"],
      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: "center",
        formatter: "用电\n类型",
        color: "#fff",
        fontSize: 16,
        fontWeight: "bold",
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 100,
          name: "用电类型",
          itemStyle: { color: "rgba(0, 0, 0, 0)" },
        },
      ],
    },
  ],
});

// 详细用电饼图配置
const detailChartOption = reactive({
  backgroundColor: "transparent",
  tooltip: {
    trigger: "item",
    formatter: "{b}",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
    },
  },
  series: [
    {
      name: "用电详情",
      type: "pie",
      radius: ["60%", "90%"],
      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      emphasis: {
        scaleSize: 20, // 高亮后扇区的放大尺寸
      },
      itemStyle: {
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 2,
      },
      label: {
        show: true,
        position: "inside",
        formatter: "{b}",
        color: "#fff",
        fontSize: 20,
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 25,
          name: "冷热站",
          itemStyle: { color: "#00a0e9" },
        },
        {
          value: 30,
          name: "空调末端",
          itemStyle: { color: "#00c957" },
        },
        {
          value: 15,
          name: "变配电室",
          itemStyle: { color: "#9370db" },
        },
        {
          value: 20,
          name: "照明系统",
          itemStyle: { color: "#00ffff" },
        },
        {
          value: 10,
          name: "其他设备",
          itemStyle: { color: "#ff7f50" },
        },
      ],
    },
    {
      name: "Access From",
      type: "pie",
      selectedMode: "single",
      radius: ["20%", "60%"],
      label: {
        position: "inner",
        fontSize: 14,
      },
      labelLine: {
        show: false,
      },
      data: [
        { value: 1548, name: "Search Engine" },
        { value: 775, name: "Direct" },
        { value: 679, name: "Marketing" },
      ],
    },
  ],
});
</script>

<style lang="less" scoped>
.search-box {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 425px;
  > div {
    height: 425px;
    flex: 1;
  }
}
</style>
