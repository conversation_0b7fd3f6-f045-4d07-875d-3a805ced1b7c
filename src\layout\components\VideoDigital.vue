<template>
  <a-row type="flex" class="video-digital-container" justify="space-between" align="bottom">
    <transition name="fade">
      <div v-if="showVideoDigtal">
        <div class="close" @click="close()"><close-outlined /></div>
        <img :src="video_digital" />
      </div>
    </transition>
  </a-row>
</template>

<script lang="ts" setup>
// developer: 谢东辉 这只不过还是一张图片
import video_digital from "/public/example/video_digital.png";
import { CloseOutlined } from "@ant-design/icons-vue";
import { computed } from "vue";
import { useStore } from "vuex";
const store = useStore();
const showVideoDigtal = computed(() => store.state.showVideoDigtal);
const close = async () => {
  store.commit("changeVideoDigtalStatus", false);
};
</script>
<style lang="less" scoped>
.video-digital-container {
  position: absolute;
  top: 815px;
  left: 4650px;
  pointer-events: auto;
}
.video-digital-container .close {
  position: absolute;
  width: 50px;
  font-size: 50px;
  color: #fff;
  height: 50px;
  left: 900px;
  top: 30px;
  cursor: pointer;
}
</style>
