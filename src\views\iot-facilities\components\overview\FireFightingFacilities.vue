<template>
  <PageCard :title="'消防设施'">
    <ul>
      <li v-for="i in [1, 2, 3, 4, 5, 6]" :key="i">
        <div class="top">消火栓泵栓泵</div>
        <div class="bottom">
          <img src="@/assets/pages-icon/iot-facilities/xhsb.png" alt="" />
          <div class="b-value">
            <span>5</span>
            个
          </div>
        </div>
        <!-- <a-divider type="vertical" style="border-color: #fff"/> -->
      </li>
    </ul>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
// import { ref } from "vue";
</script>

<style lang="less" scoped>
.page-card {
  height: 255px;
  ul {
    display: flex;
    height: 100%;
    align-items: center;
    li::after {
      content: "";
      width: 2.32px;
      height: 98.6px;
      background: #fff;
      box-shadow: 0 2.32px 2.32px 0 #00000040;
      position: absolute;
      right: 8px;
      top: 10px;
    }
    li:nth-child(6)::after {
      display: none;
    }
    li {
      position: relative;
      color: #ffffff;
      flex: 1;
      height: 120px;
      padding: 8px 16px;
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      .top {
        font-size: 21.75px;
        font-weight: 500;
        margin-left: 16px;
        position: relative;
      }
      .top::before {
        content: "";
        width: 5.22px;
        height: 21.75px;
        background: #4eedff;
        position: absolute;
        left: -16px;
        top: 7px;
        margin-right: 8px;
      }
      .bottom {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 21.75px;
        img {
          // width: 43.5px;
          // height: 43.5px;
          margin-right: 32px;
        }
        .b-value {
          line-height: 12px;
          display: flex;
          align-items: flex-end;
          span {
            color: #4eedff;
            font-size: 43.5px;
            display: inline-block;
            line-height: 40px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}
</style>
