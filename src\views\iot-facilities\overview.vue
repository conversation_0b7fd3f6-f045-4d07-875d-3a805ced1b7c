<template>
  <PageLayout>
    <template v-slot:leftcontainer>
      <DeviceOverview />
      <DeviceStatus />
    </template>
    <template v-slot:rightcontainer>
      <!-- 消防设施 -->
      <FireFightingFacilities />
      <!-- 报警统计 -->
      <AlarmStatistics />
      <div class="fire">
        <!-- 消防事件 -->
        <FireEvent />
        <!-- 消防预案 -->
        <FirePlan />
      </div>
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import DeviceOverview from "@/views/iot-facilities/components/overview/DeviceOverview.vue";
import DeviceStatus from "@/views/iot-facilities/components/overview/DeviceStatus.vue";
import FireFightingFacilities from "@/views/iot-facilities/components/overview/FireFightingFacilities.vue";
import AlarmStatistics from "@/views/iot-facilities/components/overview/AlarmStatistics.vue";
import FireEvent from "@/views/iot-facilities/components/overview/FireEvent.vue";
import FirePlan from "@/views/iot-facilities/components/overview/FirePlan.vue";
</script>

<style scoped>
.fire {
  display: flex;
  justify-content: space-between;
  height: 465px;
  width: 100%;
}
</style>
