<template>
  <block-card title="故障报警列表">
    <div class="alarm-container">
      <div class="alarm-stats">
        <div class="stat-card critical">
          <div class="stat-icon">⚠</div>
          <div class="stat-info">
            <div class="stat-value">{{ alarmStats.critical }}</div>
            <div class="stat-label">严重</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">⚡</div>
          <div class="stat-info">
            <div class="stat-value">{{ alarmStats.warning }}</div>
            <div class="stat-label">警告</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">ℹ</div>
          <div class="stat-info">
            <div class="stat-value">{{ alarmStats.info }}</div>
            <div class="stat-label">提示</div>
          </div>
        </div>
      </div>

      <div class="alarm-list">
        <div class="list-header">
          <span class="header-item">级别</span>
          <span class="header-item">设备名称</span>
          <span class="header-item">故障描述</span>
          <span class="header-item">时间</span>
          <span class="header-item">状态</span>
        </div>

        <div class="list-content">
          <div
            v-for="(alarm, index) in alarmList"
            :key="index"
            class="alarm-item"
            :class="alarm.level"
          >
            <div class="alarm-level">
              <div class="level-indicator" :class="alarm.level">
                <span v-if="alarm.level === 'critical'">⚠</span>
                <span v-else-if="alarm.level === 'warning'">⚡</span>
                <span v-else>ℹ</span>
              </div>
            </div>

            <div class="alarm-device">{{ alarm.device }}</div>
            <div class="alarm-description">{{ alarm.description }}</div>
            <div class="alarm-time">{{ alarm.time }}</div>

            <div class="alarm-status">
              <span
                class="status-badge"
                :class="alarm.status === '已处理' ? 'resolved' : 'pending'"
              >
                {{ alarm.status }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </block-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import BlockCard from "@/components/BlockCard.vue";

const alarmStats = ref({
  critical: 2,
  warning: 5,
  info: 8,
});

const alarmList = ref([
  {
    level: "critical",
    device: "配电柜-01",
    description: "过载保护触发",
    time: "10:25",
    status: "处理中",
  },
  {
    level: "warning",
    device: "空调主机-A1",
    description: "温度传感器异常",
    time: "09:45",
    status: "已处理",
  },
  {
    level: "critical",
    device: "消防泵-01",
    description: "水压不足",
    time: "09:12",
    status: "处理中",
  },
  {
    level: "warning",
    device: "电梯-02",
    description: "门锁故障",
    time: "08:30",
    status: "已处理",
  },
  {
    level: "info",
    device: "照明控制器-03",
    description: "定时开关异常",
    time: "08:15",
    status: "处理中",
  },
  {
    level: "warning",
    device: "监控摄像头-15",
    description: "画面模糊",
    time: "07:50",
    status: "已处理",
  },
  {
    level: "info",
    device: "门禁系统-A区",
    description: "刷卡响应慢",
    time: "07:20",
    status: "处理中",
  },
]);
</script>

<style scoped lang="less">
.alarm-container {
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 25px;

  .alarm-stats {
    display: flex;
    gap: 20px;

    .stat-card {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 20px;
      border-radius: 8px;
      border: 2px solid;

      &.critical {
        background: rgba(255, 77, 79, 0.1);
        border-color: rgba(255, 77, 79, 0.3);

        .stat-icon {
          color: #ff4d4f;
          background: rgba(255, 77, 79, 0.2);
        }

        .stat-value {
          color: #ff4d4f;
        }
      }

      &.warning {
        background: rgba(255, 193, 7, 0.1);
        border-color: rgba(255, 193, 7, 0.3);

        .stat-icon {
          color: #ffc107;
          background: rgba(255, 193, 7, 0.2);
        }

        .stat-value {
          color: #ffc107;
        }
      }

      &.info {
        background: rgba(78, 237, 255, 0.1);
        border-color: rgba(78, 237, 255, 0.3);

        .stat-icon {
          color: #4eedff;
          background: rgba(78, 237, 255, 0.2);
        }

        .stat-value {
          color: #4eedff;
        }
      }

      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
      }

      .stat-info {
        .stat-value {
          font-family: "HarmonyOS Sans SC";
          font-size: 32px;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 5px;
        }

        .stat-label {
          font-family: "HarmonyOS Sans SC";
          font-size: 22px;
          color: #ffffff;
          opacity: 0.8;
        }
      }
    }
  }

  .alarm-list {
    flex: 1;
    display: flex;
    flex-direction: column;

    .list-header {
      display: flex;
      padding: 15px 20px;
      background: rgba(78, 237, 255, 0.1);
      border: 1px solid rgba(78, 237, 255, 0.3);
      border-radius: 8px 8px 0 0;

      .header-item {
        font-family: "HarmonyOS Sans SC";
        font-size: 26px;
        color: #4eedff;
        font-weight: 600;
        text-align: center;

        &:nth-child(1) {
          flex: 0.8;
        }
        &:nth-child(2) {
          flex: 1.2;
        }
        &:nth-child(3) {
          flex: 1.5;
        }
        &:nth-child(4) {
          flex: 0.8;
        }
        &:nth-child(5) {
          flex: 0.8;
        }
      }
    }

    .list-content {
      flex: 1;
      border: 1px solid rgba(78, 237, 255, 0.2);
      border-top: none;
      border-radius: 0 0 8px 8px;
      overflow-y: auto;

      .alarm-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid rgba(78, 237, 255, 0.1);
        transition: background-color 0.3s ease;

        &:hover {
          background: rgba(78, 237, 255, 0.05);
        }

        &:last-child {
          border-bottom: none;
        }

        .alarm-level {
          flex: 0.8;
          display: flex;
          justify-content: center;

          .level-indicator {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;

            &.critical {
              background: rgba(255, 77, 79, 0.2);
              color: #ff4d4f;
            }

            &.warning {
              background: rgba(255, 193, 7, 0.2);
              color: #ffc107;
            }

            &.info {
              background: rgba(78, 237, 255, 0.2);
              color: #4eedff;
            }
          }
        }

        .alarm-device {
          flex: 1.2;
          font-family: "HarmonyOS Sans SC";
          font-size: 22px;
          color: #ffffff;
          text-align: center;
        }

        .alarm-description {
          flex: 1.5;
          font-family: "HarmonyOS Sans SC";
          font-size: 22px;
          color: #ffffff;
          opacity: 0.9;
          text-align: center;
        }

        .alarm-time {
          flex: 0.8;
          font-family: "HarmonyOS Sans SC";
          font-size: 22px;
          color: #ffffff;
          opacity: 0.8;
          text-align: center;
        }

        .alarm-status {
          flex: 0.8;
          display: flex;
          justify-content: center;

          .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-family: "HarmonyOS Sans SC";
            font-size: 20px;
            font-weight: 500;

            &.resolved {
              background: rgba(82, 196, 26, 0.2);
              color: #52c41a;
              border: 1px solid rgba(82, 196, 26, 0.3);
            }

            &.pending {
              background: rgba(255, 193, 7, 0.2);
              color: #ffc107;
              border: 1px solid rgba(255, 193, 7, 0.3);
            }
          }
        }
      }
    }
  }
}
</style>
