const { defineConfig } = require("@vue/cli-service");
// const CompressionWebpackPlugin = require("compression-webpack-plugin");
const isProd = process.env.ENV === "production";
module.exports = defineConfig({
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        },
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-pxtorem")({
              rootValue: 100, // 1rem = 10px
              propList: ["*"], //是一个存储哪些将被转换的属性列表，这里设置为["*"]全部，假设需要仅对边框进行设置，可以写]['*','!border*']
              exclude: /node_modules/i,
              //selectorBlackList :['.box'],//，那例如fs-xl类名，里面有关px的样式将不被转换，这里也支持正则写法。
              // replace: true, //替换包含rems的规则。
              // mediaQuery: false, //（布尔值）允许在媒体查询中转换px。
              // minPixelValue: 0, //设置要替换的最小像素值(3px会被转rem)。 默认 0
            }),
          ],
        },
      },
    },
  },
  devServer: {
    hot: true,
    liveReload: true,
    watchFiles: {
      paths: ["src/**/*", "public/**/*"],
      options: {
        usePolling: false,
      },
    },
    // proxy: {
    //   "/api/oauth": {
    //     target: VUE_APP_AuthUrl,
    //     changeOrigin: true,
    //     wx: true,
    //     pathRewrite: {
    //       "^/api/oauth": "/api/oauth",
    //     },
    //   },
    // },
  },
  productionSourceMap: !isProd,
  publicPath: "./",
  runtimeCompiler: true, //关键点在这
});
