<template>
  <PageCard class="page-card" title="车辆进出统计">
    <div class="vehicle-stats">
      <div class="stats-numbers">
        <div class="stat-card large">
          <div class="stat-icon in">
            <span>停</span>
          </div>
          <div class="stat-info">
            <div class="stat-label">当前在场车辆</div>
            <div class="stat-value">{{ vehicleData.inCount }}</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon out">
            <span>进</span>
          </div>
          <div class="stat-info">
            <div class="stat-label">今日进场</div>
            <div class="stat-value">{{ vehicleData.outCount }}</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon current">
            <span>出</span>
          </div>
          <div class="stat-info">
            <div class="stat-label">今日出场</div>
            <div class="stat-value">{{ vehicleData.currentCount }}</div>
          </div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageCard from "@/components/PageCard.vue";
// import CommonChart from "@/components/CommonChart.vue";

// const timeRange = ref("today");

const vehicleData = ref({
  inCount: 245,
  outCount: 198,
  currentCount: 156,
});

// const chartOption = computed(() => ({
//   tooltip: {
//     trigger: "axis",
//     backgroundColor: "rgba(0, 26, 40, 0.8)",
//     borderColor: "#4eedff",
//     textStyle: {
//       color: "#ffffff",
//     },
//   },
//   legend: {
//     data: ["进入", "驶出"],
//     textStyle: {
//       color: "#ffffff",
//     },
//     top: 10,
//   },
//   grid: {
//     left: "3%",
//     right: "4%",
//     bottom: "3%",
//     top: "15%",
//     containLabel: true,
//   },
//   xAxis: {
//     type: "category",
//     data: ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00"],
//     axisLine: {
//       lineStyle: {
//         color: "#4eedff",
//       },
//     },
//     axisLabel: {
//       color: "#ffffff",
//     },
//   },
//   yAxis: {
//     type: "value",
//     axisLine: {
//       lineStyle: {
//         color: "#4eedff",
//       },
//     },
//     axisLabel: {
//       color: "#ffffff",
//     },
//     splitLine: {
//       lineStyle: {
//         color: "rgba(78, 237, 255, 0.2)",
//       },
//     },
//   },
//   series: [
//     {
//       name: "进入",
//       type: "line",
//       data: [12, 8, 35, 42, 38, 25, 15],
//       smooth: true,
//       lineStyle: {
//         color: "#4eedff",
//       },
//       itemStyle: {
//         color: "#4eedff",
//       },
//       areaStyle: {
//         color: {
//           type: "linear",
//           x: 0,
//           y: 0,
//           x2: 0,
//           y2: 1,
//           colorStops: [
//             { offset: 0, color: "rgba(78, 237, 255, 0.3)" },
//             { offset: 1, color: "rgba(78, 237, 255, 0.05)" },
//           ],
//         },
//       },
//     },
//     {
//       name: "驶出",
//       type: "line",
//       data: [8, 15, 28, 38, 45, 32, 18],
//       smooth: true,
//       lineStyle: {
//         color: "#ff6b6b",
//       },
//       itemStyle: {
//         color: "#ff6b6b",
//       },
//       areaStyle: {
//         color: {
//           type: "linear",
//           x: 0,
//           y: 0,
//           x2: 0,
//           y2: 1,
//           colorStops: [
//             { offset: 0, color: "rgba(255, 107, 107, 0.3)" },
//             { offset: 1, color: "rgba(255, 107, 107, 0.05)" },
//           ],
//         },
//       },
//     },
//   ],
// }));
</script>

<style scoped lang="less">
.page-card {
  width: 552px;
  height: 400px;
}
.vehicle-stats {
  padding: 30px;
  // height: 100%;
  // display: flex;
  // flex-direction: column;

  .stats-numbers {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    // gap: 20px;

    .stat-card {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 20px;
      background: rgba(0, 26, 40, 0.3);
      border: 1px solid rgba(78, 237, 255, 0.2);
      border-radius: 8px;
      &.large {
        grid-column: span 2;
        .stat-info {
          display: flex;
          align-items: center;
          .stat-value {
            margin-left: 30px;
            font-size: 48px;
          }
        }
      }
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "HarmonyOS Sans SC";
        font-size: 28px;
        font-weight: 700;
        color: #ffffff;

        &.in {
          background: linear-gradient(135deg, #4eedff, #00d4ff);
        }

        &.out {
          background: linear-gradient(135deg, #ff6b6b, #ff5252);
        }

        &.current {
          background: linear-gradient(135deg, #ffd93d, #ffb300);
        }
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-family: "HarmonyOS Sans SC";
          font-size: 36px;
          font-weight: 700;
          color: #4eedff;
          line-height: 1;
          margin-bottom: 5px;
        }
        .stat-value::after {
          position: relative;
          content: "辆";
          bottom: 0;
          font-size: 20px;
          left: 8px;
          color: #fff;
          font-weight: 100;
        }

        .stat-label {
          font-family: "HarmonyOS Sans SC";
          font-size: 24px;
          color: #ffffff;
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
