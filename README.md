# janke-transport-digital-twin-platform

1. 在assets/maps/buildings.json中配置建筑和楼层信息，后面有其他的映射关系可以继续写在maps文件夹下
2. 在utils/buildingMapper.ts中获取建筑和楼层信息


### 兼容ue4
从UE4获取到的浏览器信息如下：
> "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) WebInterface/++UE4+Release-4.27-CL-18319896 UnrealEngine/4.27.2-18319896+++UE4+Release-4.27 Chrome/59.0.3071.15 Safari/537.36"


1. 目前只做了Chrome 59版本的兼容，后面如果有Safari浏览器，继续做兼容处理
2. 兼容处理查看  *babel.config.js*
3. 


