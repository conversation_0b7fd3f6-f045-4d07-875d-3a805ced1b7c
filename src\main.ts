import { createApp } from "vue";
import mitt from "mitt";
import Antd from "ant-design-vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "@/utils/rem";
// import ueComm from "@/utils/ueComm";
import "ant-design-vue/dist/antd.css";
import "animate.css";
import "@/style/animate.less";
import "@/style/antdCustom.less";
import "@/style/common.less";
import "@/style/normal.less";
import "@/style/theme.less";
import "vue3-carousel-3d/dist/index.css";
// import "@/style/page.less";
// import "@/assets/air/qweather-icons.css";
// import "@/style/custom.less";
const bus = mitt();
const app = createApp(App);

// 全局注册UE通信工具
// app.config.globalProperties.$ueComm = ueComm;
app.config.globalProperties.$bus = bus;
// 初始化UE全局监听
// ueComm.initGlobalListener();
app.use(store).use(router).use(Antd).mount("#app");
