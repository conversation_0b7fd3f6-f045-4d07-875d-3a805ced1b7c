<!-- <template>
  <page-layout :showBuilding="false">
    <template v-slot:leftcontainer>
      <div style="margin-bottom: 110px">
        <park-health style="height: 410px" />
      </div>

      <a-row type="flex">
        <park-access />
        <div style="width: 1706px; margin-left: 107px">
          <smart-restaurant style="height: 624px; margin-bottom: 106px" />
          <park-properties style="height: 679px" />
        </div>
      </a-row>
    </template>
    <template v-slot:rightcontainer>
      <div>
        <park-security style="height: 506px" />
      </div>
      <div style="margin: 145px 0">
        <park-carbon-emissions style="height: 577px" />
      </div>
      <div>
        <park-equipment style="height: 506px" />
      </div>
    </template>
  </page-layout>
</template> -->

<script lang="ts" setup>
// import PageLayout from "@/components/PageLayout.vue";
// import ParkHealth from "@/components/home/<USER>";
// import ParkAccess from "@/components/home/<USER>";
// import ParkCarbonEmissions from "@/components/home/<USER>";
// import ParkEquipment from "@/components/home/<USER>";
// import ParkProperties from "@/components/home/<USER>";
// import SmartRestaurant from "@/components/home/<USER>";
// import ParkSecurity from "@/components/home/<USER>";
</script>
