<template>
  <PageLayout :showFloor="false" :showBuilding="false">
    <template v-slot:leftcontainer>
      <!-- 设备概览 -->
      <DeviceOverview />
      <!-- <Suspense>
        <DeviceOverview />
        <template #fallback>
          <div>加载中...</div>
        </template>
      </Suspense> -->
      <!-- 设备状态 -->
      <DeviceStatus />
    </template>
    <template v-slot:rightcontainer>
      <!-- 能耗概览 -->
      <EnergyOverview />
      <!-- 能耗趋势 -->
      <EnergyTrends />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import DeviceOverview from "@/views/energy/components/overview/DeviceOverview.vue";
import DeviceStatus from "@/views/energy/components/overview/DeviceStatus.vue";
import EnergyOverview from "@/views/energy/components/overview/EnergyOverview.vue";
import EnergyTrends from "@/views/energy/components/overview/EnergyTrends.vue";
</script>
