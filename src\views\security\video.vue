<template>
  <div class="security-video-container">
    <h2>视频监控</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <CameraList />
        <VideoPlayback />
      </div>
      <div class="right-panel">
        <LiveMonitoring />
        <AbnormalEvents />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import CameraList from "@/components/security/video/CameraList.vue";
// import VideoPlayback from "@/components/security/video/VideoPlayback.vue";
// import LiveMonitoring from "@/components/security/video/LiveMonitoring.vue";
// import AbnormalEvents from "@/components/security/video/AbnormalEvents.vue";
</script>

<style scoped lang="less">
.security-video-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
