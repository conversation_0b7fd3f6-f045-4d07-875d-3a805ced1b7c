<template>
  <a-layout :class="[theme, 'app-wrapper ht100']">
    <!-- <web-rtc v-show="currentWebRtcFlag" /> -->
    <div class="app-main-container ht100">
      <div class="menu-header-container">
        <menu-header></menu-header>
      </div>
      <div class="layout-content">
        <app-main class="content-main" />
        <!-- <tool-control-area />
        <video-digital />
        <menu-legend v-if="menuLegendVisible" />
        <over-menu-legend v-else-if="overMenuLegendVisible" /> -->
      </div>
    </div>
  </a-layout>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, provide, computed, getCurrentInstance } from "vue";

import AppMain from "@/layout/components/AppMain.vue";
import MenuHeader from "@/layout/components/MenuHeader.vue";
import resizeBus from "@/layout/mixin/ResizeHandler";
// import WebRtc from "@/components/WebRtc.vue";
// import MenuLegend from "@/layout/components/MenuLegend.vue";
// import OverMenuLegend from "@/layout/components/OverMenuLegend.vue";
// import ToolControlArea from "@/layout/components/ToolControlArea.vue";
// import VideoDigital from "@/layout/components/VideoDigital.vue";
// import { useRouter } from "vue-router";
import { useStore } from "vuex";
// const router = useRouter();
const store = useStore();
// const currentRoute = router.currentRoute;
const ins = getCurrentInstance();

const bus = ins.appContext.config.globalProperties.$bus;
// 把bus 作为一个方法对象为全局组建提供
provide("bus", bus);
// const currentWebRtcFlag = computed(
//   () => !!currentRoute.value.meta.hasWebRtc || store.state.webRtcVisible,
// );
// const menuLegendVisible = computed(() => !currentRoute.value.meta.disabledMenuLegend);
// const overMenuLegendVisible = computed(() => !currentRoute.value.meta.disabledOverMenuLegend);
const theme = computed(() => store.state.theme);

onMounted(() => {
  resizeBus.init(() => {
    store.commit("initSreenSize", {
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight,
      flag: document.documentElement.clientWidth > 1400,
    });
  });
  // setTimeout(() => {
  //   changeTheme();
  // }, 2000);
});
//监听弹框
// window?.addResponseEventListener("CAMERA", (e: any) => {
//   const json = JSON.parse(e);
//   console.log(json);
//   if (json.type === "CAMERA") {
//     setTimeout(() => {
//       store.commit("changeVideoDigtalStatus", true);
//     }, 2500);
//   }
//   if (json.type === "EnterBuilding") {
//     setTimeout(() => {
//       bus.emit("checkedBuilding", "上海建科集团智慧园区管理系统（4号楼）");
//       bus.emit("routeChange", "/introduce");
//     }, 2500);
//   }
//   if (json.type === "EnterPark") {
//     setTimeout(() => {
//       bus.emit("routeChange", "/home");
//     }, 2500);
//   }
// });
//切换楼层
bus.on("SwitchFloors", (index: string) => {
  window.uee({ type: "ResetMEP", ResetMEP: "" });
  window.uee({ type: "SwitchFloors", SwitchFloors: index });
});
//切换二级子系统
bus.on("ChooseSystem", (index: string) => {
  window.uee({ type: "ChooseSystem", ChooseSystem: index + "_" + store.state.currentFloor });
});
//系统显示重置
bus.on("ResetMEP", () => {
  window.uee({ type: "ResetMEP", ResetMEP: "" });
});
//定位
bus.on("AlarmEvents", (index: string) => {
  window.uee({ type: "AlarmEvents", AlarmEvents: index });
});
//摄像头定位 （未接入）
bus.on("ProduceICON", (index: string) => {
  window.uee({ type: "ProduceICON", ProduceICON: index + "_" + store.state.currentFloor });
});
//环境类别
bus.on("typeEnvironl", (typeName: string) => {
  const params = new Object();
  params["typeEnvironl"] = typeName;
  params[typeName] = store.state.currentFloor;
  window.uee(params);
});
//菜单切换
bus.on("SwitchMenu", (index: string) => {
  window.uee({ type: "SwitchMenu", SwitchMenu: index });
});
bus.on("Return", (index: string) => {
  window.uee({ type: "Return", Return: index });
});
// const changeTheme = () => {
//   store.commit("changeTheme", "dark");
// };
onUnmounted(() => {
  resizeBus.destory();
});
</script>

<style scoped lang="less">
.app-wrapper {
  position: relative;
  font-family: "HarmonyOS Sans SC";
  font-style: normal;
  font-weight: 500;
  background: var(--wrapper-bg);
  .layout-content {
    display: flex;
    flex-direction: row;
    padding-top: 150px;
  }
  .app-main-container {
    pointer-events: none;
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }
  .content-main {
    background: transparent;
    width: 100%;
    height: 100%;
    z-index: 9;
    flex: auto;
  }
  .menu-header-container {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 11;
  }
}
</style>
