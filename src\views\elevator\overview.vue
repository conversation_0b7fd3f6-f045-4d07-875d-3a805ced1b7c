<template>
  <div class="elevator-overview-container">
    <h2>电梯管理总览</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <ElevatorStatus />
        <OperationalStatistics />
      </div>
      <div class="right-panel">
        <ElevatorMap />
        <MaintenanceSchedule />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import ElevatorStatus from "@/components/elevator/overview/ElevatorStatus.vue";
// import OperationalStatistics from "@/components/elevator/overview/OperationalStatistics.vue";
// import ElevatorMap from "@/components/elevator/overview/ElevatorMap.vue";
// import MaintenanceSchedule from "@/components/elevator/overview/MaintenanceSchedule.vue";
</script>

<style scoped lang="less">
.elevator-overview-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
