<template>
  <div class="table-container" ref="tableContainerRef">
    <a-table
      size="small"
      :columns="tableColumns"
      :data-source="tableDatasource"
      :pagination="tablePagination"
      :customRow="
        (record) => {
          return {
            onClick: () => tableClick(record),
          };
        }
      "
      :loading="tableLoading"
      @change="handleTableChange"
      :rowKey="props.rowKey"
      :rowClassName="props.rowClassName"
      :scroll="scroll"
    >
      <template #bodyCell="{ column, text, index, record }">
        <slot name="column" :column="column" :index="index" :text="text" :record="record"></slot>
      </template>
      <div v-if="tablePagination" class="total-tips">共有{{ tablePagination.total }}条记录</div>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import _ from "lodash";
import { ref, nextTick, computed, watch, onMounted, toRaw } from "vue";
import type { TableProps, TableColumnType } from "ant-design-vue";
import { useStore } from "vuex";

const props = defineProps<{
  dataSource: TableProps["dataSource"];
  pagination: object | boolean;
  columns: Array<TableColumnType>;
  updateLayout?: boolean;
  loading?: boolean;
  noIndex?: boolean;
  rowKey?: string;
  rowClassName?: any;
}>();
const store = useStore();
const emit = defineEmits<{
  (e: "change", config: object): void;
  (e: "click", params: object): void;
}>();
const tableClick = (e: any) => {
  emit("click", e);
};
const tableContainerRef = ref(null);
const scroll = ref<TableProps["scroll"]>({});

const tableColumns = computed(() => {
  let orderColumn: TableColumnType = {
    title: "序号",
    width: "60px",
    fixed: "left",
    customRender: ({ index }) => `${index + 1}`,
  };
  if (props.pagination) {
    orderColumn = {
      title: "序号",
      width: "60px",
      fixed: "left",
      customRender: ({ index }) =>
        `${index + 1 + (tablePagination.value.current - 1) * tablePagination.value.pageSize}`,
    };
  }
  if (props.noIndex) {
    return props.columns;
  }
  return _.concat([orderColumn], props.columns);
});

const tableDatasource = computed(() => props.dataSource);
// const pagination = computed(() => props.pagination);
const tableLoading = computed(() => props.loading);
const tablePagination = ref<object | boolean>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  buildOptionText: (size: any) => {
    let text;
    if (size.value == "0") {
      text = "全部";
    } else {
      text = `${size.value} 条/页`;
    }
    return text;
  },
  showTotal: (total: any) => `共有 ${total} 条记录`,
});
const handleTableChange: TableProps["onChange"] = (pag, filters: any, sorter: any) => {
  initPagination(pag);
  emit("change", { pag: toRaw(pag), filters, sorter });
};
const initScroll = async () => {
  if (tableContainerRef.value) {
    await nextTick();
    if (tableContainerRef.value) {
      const { offsetHeight } = tableContainerRef.value;
      scroll.value = { y: tablePagination.value ? offsetHeight - 39 - 54 : offsetHeight - 39 };
    }
  }
};
const initPagination = (option: object | boolean) => {
  if (option) {
    tablePagination.value = Object.assign(tablePagination.value, toRaw(option));
  } else {
    tablePagination.value = false;
  }
};
onMounted(async () => {
  initScroll();
});

watch(
  () => store.state.screenSize,
  async () => {
    initScroll();
  },
  { deep: true },
);
watch(
  () => props.updateLayout,
  async (val, preval) => {
    if (val !== preval) {
      initScroll();
    }
  },
);
watch(
  () => props.pagination,
  async (val) => {
    initPagination(val);
  },
  {
    deep: true,
    immediate: true,
  },
);
</script>

<style scoped lang="less">
.table-container {
  position: relative;
  flex: auto;
  .total-tips {
    position: absolute;
    bottom: 0px;
    left: 10px;
  }
}
</style>
