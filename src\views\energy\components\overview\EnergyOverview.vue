<template>
  <PageCard :title="'能耗概览'">
    <div class="nenghao-overview">
      <div>
        <div class="item" v-for="item in [1, 2, 3, 4, 5, 6]" :key="item">
          <div>
            <div>
              <span class="fz36">1000</span>
              <span class="unit">kWh</span>
            </div>
            <div>今日用电量</div>
          </div>
          <div>
            <div>
              <span class="fz36">1000</span>
              <span class="unit">%</span>
              <span class="up" v-if="item % 2 == 1">
                <img src="@/assets/pages-icon/energy/up_arrow.png" alt="" srcset="" />
              </span>
              <span class="down" v-else>
                <img src="@/assets/pages-icon/energy/down_arrow.png" alt="" srcset="" />
              </span>
            </div>
            <div>今日趋势同比</div>
          </div>
          <div></div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
</script>

<style lang="less" scoped>
.nenghao-overview {
  color: #fff;
  font-size: 22px;
  > div {
    display: flex;
    flex-wrap: wrap;
  }
  .item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px;
    text-align: center;
    width: 400px;
    height: 140px;
  }
  .item::after {
    width: 2px;
    height: 80%;
    position: absolute;
    right: 0px;
    top: 10%;
    content: "";
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      #ccc 48.96%,
      rgba(255, 255, 255, 0)
    );
  }
  .item:nth-child(3n)::after {
    display: none !important;
  }
  .up {
    color: red;
    font-weight: 900;
    line-height: 50px;
    font-size: 30px;
    margin-left: 10px;
  }
  .down {
    color: green;
    font-weight: 900;
    line-height: 50px;
    font-size: 30px;
    margin-left: 10px;
  }
  .unit {
    margin: 0 12px;
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
</style>
