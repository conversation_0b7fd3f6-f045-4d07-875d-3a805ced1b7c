.left-container-enter-active {
  animation: slideInLeft 0.5s;
}
.left-container-leave-active {
  animation: slideOutLeft 0.5s;
}
.right-container-enter-active {
  animation: slideInRight 0.5s;
}
.right-container-leave-active {
  animation: slideOutRight 0.5s;
}
.fade-enter-active {
  animation: fadeIn 0.5s;
}
.fade-leave-active {
  animation: fadeOut 0.5s;
}
.bar-btn-container-enter-active {
  animation: fadeInLeft 0.5s;
}
.bar-btn-container-leave-active {
  animation: fadeOutLeft 0.1s;
}
.breate {
  box-shadow: 0 0px 2px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation-timing-function: ease-in-out;
  animation-name: breathe;
  animation-duration: 1500ms;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  border-radius: 100%;
}
@keyframes breathe {
  0% {
    opacity: 0.4;
    box-shadow: 0 1px 2px rgba(0, 147, 223, 0.4), 0 1px 1px rgba(0, 147, 223, 0.1) inset;
  }

  100% {
    opacity: 1;
    box-shadow: 0 1px 30px #0093df, 0 1px 20px #0093df inset;
  }
}
