<template>
  <PageLayout :showFloor="true" :showBuilding="true" defaultBuildingId="B">
    <template v-slot:leftcontainer>
      <AllTrend />
      <WaterStructure />
    </template>
    <template v-slot:rightcontainer>
      <TenantsWater />
      <NightWater />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import AllTrend from "@/views/energy/components/water/AllTrend.vue";
import TenantsWater from "@/views/energy/components/water/TenantsWater.vue";
import WaterStructure from "@/views/energy/components/water/WaterStructure.vue";
import NightWater from "@/views/energy/components/water/NightWater.vue";
</script>
