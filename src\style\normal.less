html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img {
  margin: 0;
  padding: 0;
}

fieldset,
img,
input,
button {
  /*fieldset组合表单中的相关元素*/
  border: none;
  padding: 0;
  margin: 0;
  outline-style: none;
}

ul,
ol {
  list-style: none; /*清除列表风格*/
}

input {
  padding-top: 0;
  padding-bottom: 0;
}

select,
input {
  vertical-align: middle;
}

select,
input,
textarea {
  font-size: 12px;
  margin: 0;
}

textarea {
  resize: none;
}

/*防止多行文本框拖动*/
img {
  border: 0;
  vertical-align: middle;
}

/*  去掉图片低测默认的3像素空白缝隙*/
table {
  border-collapse: collapse; /*合并外边线*/
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1; /*IE/7/6*/
}

a {
  text-decoration: none;
}
* {
  box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  text-decoration: none;
  font-weight: normal;
  font-size: 100%;
}

s,
i,
em {
  font-style: normal;
  text-decoration: none;
}

.ht100,
html,
body,
#app {
  height: 100%;
  background: none;
  // overflow: hidden;
}
@font-face {
  font-family: "HarmonyOS Sans SC";
  src: url("../assets/HarmonyOS_Sans_SC_Regular.ttf") format("truetype");
}
@font-face {
  font-family: "Digital Numbers";
  src: url("../assets/DS-DIGIT.TTF") format("truetype");
  font-weight: normal;
}
@font-face {
  font-family: "Digital Numbers";
  src: url("../assets/DS-DIGI.TTF") format("truetype");
  font-weight: 400;
}
#app {

  width: 5120px;
  height: 1440px;
  pointer-events: none;
  // overflow: auto;
  background: url("../assets/pages/mask.png") no-repeat;
  background-size: cover;
}
.dialog {
  animation: fadeInUp 0.5s;
}
&::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 滑块
&::-webkit-scrollbar-thumb {
  background-color: #091532;
  border-radius: 5px;
}
&::-webkit-scrollbar-thumb:hover {
  background-color: #091532;
}
// 滑道
&::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px #333;
  border-radius: 5px;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
img {

  // 添加缩放样式
  transform: scale(0.5);
  // 让图片基于中心缩放
  transform-origin: center;
}
