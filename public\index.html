<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>世博政务办公智能化管控平台</title>
    <!-- <title><%= htmlWebpackPlugin.options.title %></title> -->
    <script src="./config.js"></script>
    <!-- <script>
      if (typeof ue != "object" || typeof ue.interface != "object") {
        if (typeof ue != "object") ue = {};
        // mobile
        ue.interface = {};
        ue.interface.broadcast = function (name, data) {
          if (typeof name != "string") return;
          var args = [name, ""];
          if (typeof data != "undefined") args[1] = data;
          console.log("%c Line:21 🍑 JSON.stringify(args)", "color:#6ec1c2", JSON.stringify(args));
          var hash = encodeURIComponent(JSON.stringify(args));
          if (typeof history == "object" && typeof history.pushState == "function") {
            history.pushState({}, "", "#" + hash);
            history.pushState({}, "", "#" + encodeURIComponent("[]"));
          } else {
            document.location.hash = hash;
            document.location.hash = encodeURIComponent("[]");
          }
        };
      } else
        (function (obj) {
          // desktop
          ue.interface = {};
          ue.interface.broadcast = function (name, data) {
            if (typeof name != "string") return;
            if (typeof data != "undefined") obj.broadcast(name, JSON.stringify(data));
            else obj.broadcast(name, "");
          };
        })(ue.interface);
      // create the global ue5(...) helper function
      ue5 = ue.interface.broadcast;
      window.ue5 = ue.interface.broadcast;
    </script> -->
    <script src="./UE.js"></script>
  </head>
  <body>
    <noscript>
      <strong>
        We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without
        JavaScript enabled. Please enable it to continue.
      </strong>
    </noscript>
    <div id="app"></div>
  </body>
  <!-- <script>
    // 这里只是解决报错问题
    ue.interface.setFPS = function (fps=30) {
      ue.interface.setFPS(fps);
    };
    // ue.interface.setDebug(false);
  </script> -->
</html>
