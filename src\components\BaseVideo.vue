<template>
  <video ref="videoPlayer" class="video-js"></video>
</template>

<script lang="ts" setup>
import videojs from "video.js/dist/video.min";
import "video.js/dist/video-js.min.css";
import { watch, onMounted, onBeforeUnmount, ref } from "vue";

const props = withDefaults(
  defineProps<{
    options: object;
    index: number;
    rate: number;
  }>(),
  {
    options: () => {
      return {};
    },
    index: 0,
    rate: 0.5,
  },
);
const emit = defineEmits<{
  (e: "end", config: object): void;
}>();
watch(
  () => props.options,
  (n) => {
    changeVideoSource(n);
  },
  { deep: true },
);
watch(
  () => props.rate,
  () => {
    changeRate();
  },
);
// 在onMounted阶段进行初始化
onMounted(() => {
  initVideo();
});
// 在onBeforeUnmount阶段释放资源
onBeforeUnmount(() => {
  disposeVideo();
});

const player: any = ref(null);
const videoPlayer: any = ref(null);
// 初始化视频
const initVideo = () => {
  if (!props.options) return;
  player.value = videojs(videoPlayer.value, props.options, () => {
    changeRate();
  });
  player.value.on("ended", function () {
    emit("end", { index: props.index });
  });
};
// 视频地址发生变化时的处理
const changeVideoSource = (options: any) => {
  if (!options) return;
  console.log(options.sources);
  player.value.src(options.sources);
  player.value.play();
};
const disposeVideo = () => {
  if (player.value) {
    player.value.dispose();
  }
};
const changeRate = () => {
  player.value.playbackRate(props.rate);
};
</script>
<style lang="less" scoped>
.video-js {
  width: 100%;
  height: 100%;
}
</style>
