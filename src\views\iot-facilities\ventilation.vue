<template>
  <PageLayout>
    <template v-slot:leftcontainer>
      <!-- 设备概览 -->
      <DeviceOverview />
      <!-- 故障报警 -->
      <FaultAlarm />
    </template>
    <template v-slot:rightcontainer>
      <!-- 设备状态 -->
      <DeviceStatus />
      <!-- 运行趋势 -->
      <RunningTrend />
      <!-- <HistoricalTrend /> -->
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import DeviceOverview from "@/views/iot-facilities/components/ventilation/DeviceOverview.vue";
import FaultAlarm from "@/views/iot-facilities/components/ventilation/FaultAlarm.vue";
import DeviceStatus from "@/views/iot-facilities/components/ventilation/DeviceStatus.vue";
import RunningTrend from "@/views/iot-facilities/components/ventilation/RunningTrend.vue";
</script>

<style scoped lang="less"></style>
