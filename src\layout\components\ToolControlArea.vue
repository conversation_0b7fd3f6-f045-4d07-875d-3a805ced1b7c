<template>
  <a-row type="flex" class="tool-control-area-container" justify="space-between" align="bottom">
    <div>
      <transition name="fade">
        <Co2HeatMapLegend v-if="meteInfo.hasCo2HeatMapLegend" />
      </transition>
      <transition name="fade">
        <EnvHeatMapLegend v-if="meteInfo.hasEnvHeatMapLegend" />
      </transition>
      <transition name="fade">
        <equipment-type-switch v-if="meteInfo.hasEquipmentTypeLegend" />
      </transition>
    </div>
    <a-row type="flex" justify="end" class="flex-column" align="bottom">
      <transition name="fade">
        <floor-switch v-if="meteInfo.hasFloorSwitch" />
      </transition>
      <!-- <transition name="fade">
        <equipment-type-switch v-if="meteInfo.hasEquipmentTypeLegend" />
      </transition> -->
    </a-row>
  </a-row>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import FloorSwitch from "@/components/base/FloorSwitch.vue";
import EnvHeatMapLegend from "@/components/base/EnvHeatMapLegend.vue";
import Co2HeatMapLegend from "@/components/base/Co2HeatMapLegend.vue";
import EquipmentTypeSwitch from "@/components/base/EquipmentTypeSwitch.vue";
const router = useRouter();
const currentRoute = router.currentRoute;
const meteInfo = computed(() => currentRoute.value.meta);
</script>
<style lang="less" scoped></style>
