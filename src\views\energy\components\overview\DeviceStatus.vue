<template>
  <PageCard :title="'设备状态'">
    <SearchForm
      :fields="searchFields"
      :initialValues="initialValues"
      :defaultTimeRange="'month'"
      @timeRangeChange="handleTimeRangeChange"
      :showTimeRange="false"
      :showSearchBox="true"
    />
    <!-- <div class="search-box">
      <a-space :size="30">
        <div>
          <span>在线/离线：</span>
          <a-select
            v-model:value="status"
            style="width: 120px"
            :options="options1"
            size="large"
            @change="handleChange"
          ></a-select>
        </div>
        <div>
          <span>建筑：</span>
          <a-select
            v-model:value="build"
            style="width: 120px"
            size="large"
            @change="handleChange"
          >
            <a-select-option
              v-for="(option, optIndex) in options2"
              :key="optIndex"
              :value="option.value"
            >
              {{ option.name }}
            </a-select-option>
          </a-select>
        </div>
        <div>
          <span>楼层：</span>
          <a-select
            v-model:value="floor"
            size="large"
            style="width: 120px"
            :options="options3"
          ></a-select>
        </div>
      </a-space>
    </div> -->
    <div class="table-container">
      <BaseTable
        class="ant-table-striped"
        :columns="columns"
        :data-source="data"
        :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
        :pagination="false"
        :bordered="false"
      >
        <template v-slot:column="scoped">
          <a-row
            v-if="scoped.column.dataIndex === 'operation'"
            class="operation"
            align="middle"
            justify="center"
          >
            <!-- <a-button type="primary" size="small" @click="toDetails(scoped.record)">
            </a-button> -->
            <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
            <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
          </a-row>
        </template>
      </BaseTable>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import BaseTable from "@/components/BaseTable.vue";
import { ref, inject, watch, onMounted } from "vue";
import type { SelectProps } from "ant-design-vue";
import { Emitter } from "mitt";
// 获取bus deviceOverview 事件
const bus = inject<Emitter<any>>("bus")!;
bus.on("deviceOverview", (data: any) => {
  console.log("%c Line:80 🍞 data", "color:#93c0a4", data);
});

// 获取楼层文件
import BuildingMapper from "@/utils/buildingMapper";
const floors = ref([] as any[]);
const buildings = ref(BuildingMapper.getAllBuildings());
buildings.value.forEach((item: any) => {
  item.label = item.name;
  item.value = item.id;
  delete item.name;
});
const searchFields = ref([
  {
    type: "select" as const,
    prop: "status",
    formItem: { label: "在线/离线" },
    attrs: {
      placeholder: "请选择",
      clearable: true,
      size: "large",
    },
    options: [
      { label: "全部", value: "" },
      { label: "在线", value: "1" },
      { label: "离线", value: "2" },
    ],
    on: {
      change: (val: any) => {
        console.log("状态变化:", val);
        initialValues.value.status = val;
      },
    },
  },
  {
    type: "select" as const,
    prop: "building",
    formItem: { label: "建筑" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: buildings.value,
    on: {
      change: (val: any) => {
        initialValues.value.floor = "";
        console.log("%c Line:134 🍑 当前建筑id:", "color:#7f2b82", val);
        initialValues.value.building = val;
        floors.value = BuildingMapper.getFloorsByBuildingId(val);
        floors.value.forEach((item: any) => {
          item.label = item.name;
          item.value = item.id;
          delete item.name;
        });
        searchFields.value[2].options = floors.value;
      },
    },
  },
  {
    type: "select" as const,
    prop: "floor",
    formItem: { label: "楼层" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: floors.value,
    on: {
      change: (val: any) => {
        initialValues.value.floor = val;
      },
    },
  },
  {
    type: "radio" as const,
    prop: "timeRange",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "日", value: "currentDay" },
      { label: "月", value: "currentMonth" },
      { label: "年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {

        initialValues.value.timeRange = val;
      },
    },
  },
]);

const initialValues = ref({
  timeRange: "currentDay",
  status: "1",
  building: "",
  floor: "",
});

const handleTimeRangeChange = (timeRange: any) => {
  console.log("时间范围变化:", timeRange);
};
const columns = [
  { title: "表具编号", dataIndex: "numId" },
  { title: "设备名称", dataIndex: "name" },
  { title: "位置", dataIndex: "address" },
  { title: "在线/离线", dataIndex: "online" },
  { title: "操作", dataIndex: "operation" },
];
const data = [
  {
    key: "1",
    number: 1,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "2",
    number: 2,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "3",
    number: 3,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
];
onMounted(() => {
  floors.value = BuildingMapper.getFloorsByBuildingId(initialValues.value.building);
  floors.value.forEach((item: any) => {
    item.label = item.name;
    item.value = item.id;
    delete item.name;
  });
  searchFields.value[2].options = floors.value;
});
</script>

<style lang="less" scoped>
.page-card {
  .search-box {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    color: #fff;
    span {
      margin-right: 10px;
    }
  }
  .table-container {
    position: relative;
    height: 670px;
    overflow: hidden;
    // border: 1px solid rgba(255, 255, 255, 0.1);
    // border-radius: 4px;
  }

  // .ant-table-striped {
  //   height: 100%;
  // }

  :deep .ant-table-striped {
    position: relative;
    left: 10px;
    .table-dan {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(7, 27, 51, 1);
        color: #fff;
      }
    }
    .table-shuang {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(15, 36, 61, 1);
        color: #fff;
      }
    }
    .ant-table-thead {
      th {
        background-color: rgba(15, 41, 82, 1);
        color: #fff;
        height: 67px;
      }
    }
  }

  .ant-table-striped :deep(.ant-table-body) {
    overflow-y: auto !important;
    max-height: 590px !important;
  }

  .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar) {
    position: absolute;
    left: 10px;
    width: 3px;
    height: 8px;
  }
  img {
    margin-right: 20px;
    cursor: pointer;
  }
  img:hover {
    opacity: 0.7;
  }
  // .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar-thumb) {
  //   background-color: rgba(255, 255, 255, 0.2);
  //   border-radius: 4px;
  // }

  // .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar-track) {
  //   background-color: rgba(0, 0, 0, 0.1);
  //   border-radius: 4px;
  // }
}
</style>
