<template>
  <block-card title="现场值班人员">
    <div class="personnel-container">
      <div class="personnel-stats">
        <div class="stat-item">
          <div class="stat-icon total">
            <span>{{ personnelData.total }}</span>
          </div>
          <div class="stat-label">总人数</div>
        </div>

        <div class="stat-item">
          <div class="stat-icon online">
            <span>{{ personnelData.online }}</span>
          </div>
          <div class="stat-label">在线</div>
        </div>

        <div class="stat-item">
          <div class="stat-icon offline">
            <span>{{ personnelData.offline }}</span>
          </div>
          <div class="stat-label">离线</div>
        </div>
      </div>

      <div class="personnel-list">
        <div class="list-header">
          <span class="header-item">姓名</span>
          <span class="header-item">岗位</span>
          <span class="header-item">状态</span>
          <span class="header-item">位置</span>
        </div>

        <div class="list-content">
          <div
            v-for="(person, index) in personnelList"
            :key="index"
            class="person-item"
            :class="{ online: person.status === '在线', offline: person.status === '离线' }"
          >
            <div class="person-avatar">
              <img :src="person.avatar" :alt="person.name" />
              <div
                class="status-indicator"
                :class="person.status === '在线' ? 'online' : 'offline'"
              ></div>
            </div>

            <div class="person-info">
              <div class="person-name">{{ person.name }}</div>
              <div class="person-position">{{ person.position }}</div>
            </div>

            <div class="person-status" :class="person.status === '在线' ? 'online' : 'offline'">
              {{ person.status }}
            </div>

            <div class="person-location">{{ person.location }}</div>
          </div>
        </div>
      </div>
    </div>
  </block-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import BlockCard from "@/components/BlockCard.vue";

const personnelData = ref({
  total: 12,
  online: 9,
  offline: 3,
});

const personnelList = ref([
  {
    name: "张三",
    position: "安保主管",
    status: "在线",
    location: "1F大厅",
    // avatar: "/example/zct_1.png"
  },
  {
    name: "李四",
    position: "维修工程师",
    status: "在线",
    location: "3F机房",
    // avatar: "/example/zct_2.png"
  },
  {
    name: "王五",
    position: "保洁员",
    status: "离线",
    location: "2F办公区",
    // avatar: "/example/zct_1.png"
  },
  {
    name: "赵六",
    position: "电工",
    status: "在线",
    location: "地下室",
    // avatar: "/example/zct_2.png"
  },
  {
    name: "钱七",
    position: "安保员",
    status: "在线",
    location: "停车场",
    // avatar: "/example/zct_1.png"
  },
  {
    name: "孙八",
    position: "前台",
    status: "在线",
    location: "1F前台",
    // avatar: "/example/zct_2.png"
  },
]);
</script>

<style scoped lang="less">
.personnel-container {
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 30px;

  .personnel-stats {
    display: flex;
    justify-content: space-around;
    gap: 20px;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;

      .stat-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "HarmonyOS Sans SC";
        font-size: 32px;
        font-weight: 700;
        color: #ffffff;

        &.total {
          background: linear-gradient(135deg, #4eedff, #00d4ff);
        }

        &.online {
          background: linear-gradient(135deg, #52c41a, #73d13d);
        }

        &.offline {
          background: linear-gradient(135deg, #ff4d4f, #ff7875);
        }
      }

      .stat-label {
        font-family: "HarmonyOS Sans SC";
        font-size: 24px;
        color: #ffffff;
        opacity: 0.8;
      }
    }
  }

  .personnel-list {
    flex: 1;
    display: flex;
    flex-direction: column;

    .list-header {
      display: flex;
      padding: 15px 20px;
      background: rgba(78, 237, 255, 0.1);
      border: 1px solid rgba(78, 237, 255, 0.3);
      border-radius: 8px 8px 0 0;

      .header-item {
        flex: 1;
        font-family: "HarmonyOS Sans SC";
        font-size: 28px;
        color: #4eedff;
        font-weight: 600;
        text-align: center;

        &:first-child {
          flex: 1.5;
        }
      }
    }

    .list-content {
      flex: 1;
      border: 1px solid rgba(78, 237, 255, 0.2);
      border-top: none;
      border-radius: 0 0 8px 8px;
      overflow-y: auto;

      .person-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid rgba(78, 237, 255, 0.1);
        transition: background-color 0.3s ease;

        &:hover {
          background: rgba(78, 237, 255, 0.05);
        }

        &:last-child {
          border-bottom: none;
        }

        .person-avatar {
          position: relative;
          width: 50px;
          height: 50px;
          margin-right: 15px;

          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }

          .status-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #ffffff;

            &.online {
              background: #52c41a;
            }

            &.offline {
              background: #ff4d4f;
            }
          }
        }

        .person-info {
          flex: 1.5;

          .person-name {
            font-family: "HarmonyOS Sans SC";
            font-size: 26px;
            color: #ffffff;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .person-position {
            font-family: "HarmonyOS Sans SC";
            font-size: 20px;
            color: #ffffff;
            opacity: 0.7;
          }
        }

        .person-status {
          flex: 1;
          text-align: center;
          font-family: "HarmonyOS Sans SC";
          font-size: 24px;
          font-weight: 500;

          &.online {
            color: #52c41a;
          }

          &.offline {
            color: #ff4d4f;
          }
        }

        .person-location {
          flex: 1;
          text-align: center;
          font-family: "HarmonyOS Sans SC";
          font-size: 24px;
          color: #ffffff;
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
