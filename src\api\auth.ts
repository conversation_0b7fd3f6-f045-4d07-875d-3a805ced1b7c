import { mockLogin, mockGetUserInfo, mockLogout } from "@/mock/auth";

// 登录接口
export const login = async (username: string, password: string) => {
  try {
    const response = await mockLogin(username, password);
    return response;
  } catch (error) {
    console.log("%c Line:9 🥃 error", "color:#f5ce50", error);
    throw error;
  }
};

// 获取用户信息接口
export const getUserInfo = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) {
      throw { code: 401, message: "未找到token" };
    }
    const response = await mockGetUserInfo(token);
    return response;
  } catch (error) {
    console.log("%c Line:23 🍅 error", "color:#e41a6a", error);
    throw error;
  }
};

// 退出登录接口
export const logout = async () => {
  try {
    const response = await mockLogout();
    return response;
  } catch (error) {
    console.log("%c Line:33 🍩 error", "color:#3f7cff", error);
    throw error;
  }
};
