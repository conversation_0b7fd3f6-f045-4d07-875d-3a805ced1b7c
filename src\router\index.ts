import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import Layout from "@/layout/index.vue";
import store from "@/store";
import { initUserInfo } from "@/utils/auth";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: { requiresAuth: false },
  },
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    meta: { requiresAuth: true },
    children: [
      {
        path: "/home",
        name: "home",
        component: () => import("@/views/home/<USER>"),
        meta: {
          showBuilding: false,
          showFloor: false,
          requiresAuth: true,
        },
      },
      // 物联设施
      {
        path: "/iot-facilities",
        name: "iotFacilities",
        component: () => import("@/views/iot-facilities/index.vue"),
        redirect: "/iot-facilities/overview",
        meta: {},
        children: [
          {
            path: "overview",
            name: "iotOverview",
            component: () => import("@/views/iot-facilities/overview.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "air-conditioning",
            name: "iotAirConditioning",
            component: () => import("@/views/iot-facilities/air-conditioning.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "ventilation",
            name: "iotVentilation",
            component: () => import("@/views/iot-facilities/ventilation.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "water-supply",
            name: "iotWaterSupply",
            component: () => import("@/views/iot-facilities/water-supply.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "fire-alarm",
            name: "iotFireAlarm",
            component: () => import("@/views/iot-facilities/fire-alarm.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
        ],
      },
      // 安防设备
      {
        path: "/security",
        name: "security",
        component: () => import("@/views/security/index.vue"),
        redirect: "/security/overview",
        meta: {
          // showBuilding: true,
          // showFloor: false,
        },
        children: [
          {
            path: "overview",
            name: "securityOverview",
            component: () => import("@/views/security/overview.vue"),
            meta: {
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "video",
            name: "securityVideo",
            component: () => import("@/views/security/video.vue"),
            meta: {
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "access",
            name: "securityAccess",
            component: () => import("@/views/security/access.vue"),
            meta: {
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "perimeter",
            name: "securityPerimeter",
            component: () => import("@/views/security/perimeter.vue"),
            meta: {
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "parking",
            name: "securityParking",
            component: () => import("@/views/security/parking.vue"),
            meta: {
              showBuilding: true,
              showFloor: false,
            },
          },
        ],
      },
      // 能源管理
      {
        path: "/energy",
        name: "energy",
        component: () => import("@/views/energy/index.vue"),
        redirect: "/energy/overview",
        children: [
          {
            path: "overview",
            name: "energyOverview",
            component: () => import("@/views/energy/overview.vue"),
            meta: {
              showBuilding: false,
              showFloor: false,
            },
          },
          {
            path: "electricity",
            name: "energyElectricity",
            component: () => import("@/views/energy/electricity.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "water",
            name: "energyWater",
            component: () => import("@/views/energy/water.vue"),
            meta: {
              showBuilding: true,
              showFloor: true,
            },
          },
        ],
      },
      // 停车管理
      {
        path: "/parking",
        name: "parking",
        component: () => import("@/views/parking/index.vue"),
        redirect: "/parking/overview",
        meta: {
          // showBuilding: true,
          // showFloor: false,
        },
        children: [
          {
            path: "overview",
            name: "parkingOverview",
            component: () => import("@/views/parking/overview.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "space",
            name: "parkingSpace",
            component: () => import("@/views/parking/space.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: false,
            },
          },
          {
            path: "fee",
            name: "parkingFee",
            component: () => import("@/views/parking/fee.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: false,
            },
          },
        ],
      },
      // 电梯管理
      {
        path: "/elevator",
        name: "elevator",
        component: () => import("@/views/elevator/index.vue"),
        redirect: "/elevator/overview",
        meta: {
          // showBuilding: true,
          // showFloor: true,
        },
        children: [
          {
            path: "overview",
            name: "elevatorOverview",
            component: () => import("@/views/elevator/overview.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "operation",
            name: "elevatorOperation",
            component: () => import("@/views/elevator/operation.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: true,
            },
          },
          {
            path: "maintenance",
            name: "elevatorMaintenance",
            component: () => import("@/views/elevator/maintenance.vue"),
            meta: {
              // hasWebRtc: true, 已删除
              showBuilding: true,
              showFloor: true,
            },
          },
        ],
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const isLoggedIn = store.state.isLoggedIn;
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth !== false);

  if (requiresAuth && !isLoggedIn) {
    // 需要登录但未登录，跳转到登录页
    next("/login");
  } else if (to.path === "/login" && isLoggedIn) {
    // 已登录用户访问登录页，跳转到首页
    next("/");
  } else if (requiresAuth && isLoggedIn) {
    // 需要登录且已登录，检查用户信息
    try {
      await initUserInfo();
      next();
    } catch (error) {
      // 用户信息获取失败，跳转到登录页
      next("/login");
    }
  } else {
    next();
  }
});

export default router;
