<template>
  <!-- 总体用水趋势 -->
  <page-card :title="'建筑用电趋势（租户）'">
    <div class="electricity-all-trend">
      <div class="search-box">
        <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
          <a-radio-button value="day">今日</a-radio-button>
          <a-radio-button value="month">本月</a-radio-button>
          <a-radio-button value="year">本年</a-radio-button>
        </a-radio-group>
      </div>
      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref } from "vue";
const timeRange = ref("year");

const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "3%",
    right: "4%",
    bottom: "14%",
    top: "20%",
    containLabel: true,
  },
  // 图例配置
  legend: {
    data: ["A栋", "B栋", "C栋", "D栋", "E栋"], // 图例数据，从API获取
    icon: "roundRect",
    bottom: "0%", // 图例位置
    textStyle: {
      fontSize: 16,
      color: "#fff",
    },
  },
  tooltip: {
    trigger: "axis",
    // textStyle: {
    //   fontSize: 20,
    //   color: "#fff",
    // },
    // backgroundColor: "rgba(0, 10, 20, 0.8)",
    // borderColor: "rgba(255, 255, 255, 0.2)",
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
    },
    // splitLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(255, 255, 255, 0.1)",
    //     type: "dashed",
    //   },
    // },
  },
  yAxis: {
    type: "value",
    name: "单位：kWh",
    nameGap: 30,
    nameTextStyle: {
      fontSize: 20,
      color: "#FFFFFF",
    },
    // min: 0,
    // max: 140,
    // interval: 20,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "A栋",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 12,
      // itemStyle: {
      //   color: "#00a0e9",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#00a0e9",
      // },
      data: [8, 22, 28, 28, 30, 36, 50, 62, 68, 84, 74, 38],
    },
    {
      name: "B栋",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 12,
      // itemStyle: {
      //   color: "#ff9f00",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#ff9f00",
      // },
      data: [105, 92, 84, 85, 85, 78, 62, 50, 45, 30, 38, 75],
    },
    {
      name: "C栋",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 12,
      // itemStyle: {
      //   color: "#ff9f00",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#ff9f00",
      // },
      data: [11, 22, 33, 44, 44, 55, 55, 55, 44, 33, 89, 63],
    },
    {
      name: "D栋",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 12,
      // itemStyle: {
      //   color: "#ff9f00",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#ff9f00",
      // },
      data: [99, 55, 44, 33, 36, 78, 62, 32, 100, 85, 34, 67],
    },
  ],
});
</script>

<style lang="less" scoped>
.chart-container {
  height: 400px;
}
.search-box {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  > div {
    margin-right: 30px;
  }
}
</style>
