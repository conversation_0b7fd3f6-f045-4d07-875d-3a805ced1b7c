<template>
  <div class="parking-overview-container">
    <h2>停车管理总览</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <ParkingOccupancy />
        <VehicleFlow />
      </div>
      <div class="right-panel">
        <ParkingMap />
        <RevenueStatistics />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import ParkingOccupancy from "@/components/parking/overview/ParkingOccupancy.vue";
// import VehicleFlow from "@/components/parking/overview/VehicleFlow.vue";
// import ParkingMap from "@/components/parking/overview/ParkingMap.vue";
// import RevenueStatistics from "@/components/parking/overview/RevenueStatistics.vue";
</script>

<style scoped lang="less">
.parking-overview-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
