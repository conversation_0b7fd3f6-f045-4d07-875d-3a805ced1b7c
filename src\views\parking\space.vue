<template>
  <div class="parking-space-container">
    <h2>车位管理</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <SpaceAllocation />
        <OccupancyRate />
      </div>
      <div class="right-panel">
        <ReservationSystem />
        <SpaceUtilization />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import SpaceAllocation from "@/components/parking/space/SpaceAllocation.vue";
// import OccupancyRate from "@/components/parking/space/OccupancyRate.vue";
// import ReservationSystem from "@/components/parking/space/ReservationSystem.vue";
// import SpaceUtilization from "@/components/parking/space/SpaceUtilization.vue";
</script>

<style scoped lang="less">
.parking-space-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
