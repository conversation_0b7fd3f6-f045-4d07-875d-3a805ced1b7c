<template>
  <section class="menu-legend-container">
    <div v-for="(item, index) in menuList" :key="index" class="legend-item">
      <a-row
        type="flex"
        class="btn-container"
        v-if="checkedPath === item.routeName && item.submenuList"
      >
        <div
          v-for="(bitem, bindex) in item.submenuList"
          :key="bindex"
          @click="changeBtn(bitem)"
          :class="{ 'btn-item': true, checked: subCheckedRoute === bitem.routeName }"
        >
          {{ bitem.btnName }}
        </div>
      </a-row>
      <div @click="changeRoute(item)">
        <div :class="{ 'legend-icon': true, checked: checkedPath === item.routeName }">
          <img
            :src="`/icon/${item.iconName}${checkedPath === item.routeName ? '_checked' : ''}.svg`"
          />
        </div>

        <div :class="{ 'legend-label': true, 'label-checked': checkedPath === item.routeName }">
          {{ item.title }}
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, inject, watch } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";

const bus = inject("bus");
const store = useStore();
const router = useRouter();
const currentRoute = router.currentRoute;

const menuList = computed(() => store.state.overMenuList);
const currentPath = computed(() => currentRoute.value.path);
const checkedPath = ref("");
const subCheckedRoute = ref("");
const changeRoute = (item: any) => {
  if (currentPath.value !== item.routeName) {
    // if (item.bimId) {
    //   bus.emit("SwitchMenu", item.bimId);
    // }
    bus.emit("routeChange", item.routeName);
    checkedPath.value = item.routeName;
  } else {
    bus.emit("routeChange", "/home");
    checkedPath.value = "/home";
  }
};
watch(
  () => checkedPath.value,
  async (val, oldval) => {
    if (!val) return;
    const cr = menuList.value.find((item: any) => item.routeName === val);
    if (cr && cr.submenuList) {
      if (subCheckedRoute.value === "" || (oldval && val !== oldval)) {
        subCheckedRoute.value = cr.submenuList[0].routeName;
        bus.emit("SwitchMenu", cr.submenuList[0].bimId);
      }
    } else {
      subCheckedRoute.value = "";
      bus.emit("SwitchMenu", cr.bimId);
    }
  },
  { deep: true, immediate: true },
);
const changeBtn = (item: any) => {
  bus.emit("SwitchMenu", item.bimId);
  subCheckedRoute.value = item.routeName;
  bus.emit("routeChange", item.routeName);
};
onMounted(() => {
  if (currentRoute.value.meta.parentRouteName) {
    checkedPath.value = currentRoute.value.meta.parentRouteName;
    subCheckedRoute.value = currentRoute.value.path;
  } else {
    checkedPath.value = currentRoute.value.path;
  }
  bus.on("routeChange", (route: any) => {
    checkedPath.value = route;
  });
});
</script>
<style lang="less" scoped></style>
