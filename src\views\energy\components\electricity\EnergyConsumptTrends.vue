<template>
  <PageCard :title="'能耗趋势'">
    <div class="search-box">
      <a-space :size="20">
        <a-select
          v-model:value="value1"
          style="width: 120px"
          :options="options1"
          size="large"
          @change="handleChange"
        ></a-select>
        <a-select
          v-model:value="value2"
          size="large"
          style="width: 120px"
          :options="options2"
        ></a-select>
      </a-space>
      <a-space :size="20">
        <a-button type="primary" size="large" @click="viewDetails">详情</a-button>
        <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
          <a-radio-button value="day">今日</a-radio-button>
          <a-radio-button value="month">本月</a-radio-button>
          <a-radio-button value="year">本年</a-radio-button>
        </a-radio-group>
      </a-space>
    </div>

    <div class="chart-container">
      <common-chart :echart-obj="chartOption" />
    </div>
  </PageCard>
  <a-modal
    v-model:visible="visible"
    title="电能详细流向"
    @ok="handleOk"
    width="1920px"
    centered
    wrap-class-name="energy-consumpt-trends-detail-dialog"
  >
    <div class="details-dialog">
      <common-chart :echart-obj="chartOption" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import type { SelectProps } from "ant-design-vue";
import { ref } from "vue";

const visible = ref<boolean>(false);
const handleOk = (e: MouseEvent) => {
  console.log(e);
  visible.value = false;
};
const timeRange = ref("year");
const value1 = ref("lucy");
const value2 = ref("lucy");
const options1 = ref<SelectProps["options"]>([
  {
    value: "jack",
    label: "Jack",
  },
  {
    value: "lucy",
    label: "Lucy",
  },
  {
    value: "disabled",
    label: "Disabled",
    disabled: true,
  },
  {
    value: "yiminghe",
    label: "Yiminghe",
  },
]);
const options2 = ref<SelectProps["options"]>([
  {
    value: "lucy",
    label: "Lucy",
  },
]);

const handleChange = () => {
  console.log("view handleChange");
};
const viewDetails = () => {
  console.log("view details");
  visible.value = true;
};

// const handleSearch = (values: any) => {
//   console.log("Search values:", values);
// };

// 能耗趋势
const chartOption = ref({
  // backgroundColor: "#fff",
  // title: {
  //   subtext: "Data From lisachristina1234 on GitHub",
  // },
  grid: {
    top: "50%",
  },
  series: {
    type: "sankey",
    // layout: "none",
    // emphasis: {
    //   focus: "adjacency",
    // },
    data: [],
    links: [],
  },
});
const initChartOption = () => {
  for (let i = 0; i < 26; i++) {
    chartOption.value.series.data.push({
      name: String.fromCharCode(97 + i),
    });
    chartOption.value.series.links.push({
      source: String.fromCharCode(97 + Math.floor(i / 7)),
      target: String.fromCharCode(97 + i + 1),
      value: 10,
    });
  }
};
initChartOption();
</script>

<style lang="less" scoped>
.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 535px;
  > div {
    width: 85%;
    height: 480px;
  }
}
.search-box {
  padding: 16px 16px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // > div,
  // span,
  // button {
  //   margin-right: 30px;
  // }
}

:deep(.ant-modal-content) {
  background: rgb(0, 26, 40, 0.8) !important;
  color: #fff;
}
.details-dialog {
  width: 1440px;
  height: 900px;
}
</style>
<style lang="less">
.energy-consumpt-trends-detail-dialog {
  .ant-modal-content,
  .ant-modal-header {
    background: rgb(0, 26, 40, 0.8);
    color: #fff;
    .ant-modal-title {
      color: #fff;
    }
  }
}
</style>
