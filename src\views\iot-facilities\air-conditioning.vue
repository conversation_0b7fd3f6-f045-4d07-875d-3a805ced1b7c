<template>
  <PageLayout>
    <template v-slot:leftcontainer>
      <!-- 设备概览 -->
      <DeviceOverview />
      <!-- 故障报警 -->
      <FaultAlarm />
    </template>
    <template v-slot:rightcontainer>
      <!-- 设备状态 -->
      <DeviceStatus />
      <!-- 历史趋势 -->
      <HistoricalTrend />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import DeviceOverview from "@/views/iot-facilities/components/air-conditioning/DeviceOverview.vue";
import FaultAlarm from "@/views/iot-facilities/components/air-conditioning/FaultAlarm.vue";
import DeviceStatus from "@/views/iot-facilities/components/air-conditioning/DeviceStatus.vue";
import HistoricalTrend from "@/views/iot-facilities/components/air-conditioning/HistoricalTrend.vue";
</script>

<style scoped></style>
