interface ScreenSizeBus {
  [propName: string]: any;
}
const init = (cb: any): void => {
  let timeOutResize: any = null;
  window.onresize = (): void => {
    // 定义窗口大小变更通知事件
    if (timeOutResize) clearTimeout(timeOutResize);
    timeOutResize = setTimeout((): void => {
      cb();
    }, 200);
  };
};
const destory = (): void => {
  window.onresize = null;
};
const screenSizeBus: ScreenSizeBus = { init, destory };

export default screenSizeBus;
