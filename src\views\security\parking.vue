<template>
  <div class="security-parking-container">
    <h2>停车场管理</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <ParkingStatus />
        <VehicleRecognition />
      </div>
      <div class="right-panel">
        <ParkingRecords />
        <AbnormalEvents />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import ParkingStatus from "@/components/security/parking/ParkingStatus.vue";
// import VehicleRecognition from "@/components/security/parking/VehicleRecognition.vue";
// import ParkingRecords from "@/components/security/parking/ParkingRecords.vue";
// import AbnormalEvents from "@/components/security/parking/AbnormalEvents.vue";
</script>

<style scoped lang="less">
.security-parking-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
