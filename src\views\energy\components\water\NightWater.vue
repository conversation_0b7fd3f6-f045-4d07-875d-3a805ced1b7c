<template>
  <page-card :title="'夜间用水趋势'">
    <div class="search-box">
      <a-space :size="30">
        <div>
          <span>建筑：</span>
          <a-select
            v-model:value="value1"
            style="width: 120px"
            :options="options1"
            size="large"
            @change="handleChange"
          ></a-select>
        </div>
        <div>
          <span>楼层：</span>
          <a-select
            v-model:value="value2"
            size="large"
            style="width: 120px"
            :options="options2"
          ></a-select>
        </div>
        <div>
          <span>时间：</span>
          <a-time-picker v-model:value="value3" placeholder="请选择时间" />
        </div>
      </a-space>
    </div>

    <div class="chart-container">
      <common-chart :echart-obj="chartOption" />
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import type { SelectProps } from "ant-design-vue";
// import dayjs, { Dayjs } from 'dayjs';
import { ref } from "vue";
const value1 = ref("lucy");
const value2 = ref("lucy");
const value3 = ref("");
const options1 = ref<SelectProps["options"]>([
  {
    value: "jack",
    label: "Jack",
  },
  {
    value: "lucy",
    label: "Lucy",
  },
  {
    value: "disabled",
    label: "Disabled",
    disabled: true,
  },
  {
    value: "yiminghe",
    label: "Yiminghe",
  },
]);
const options2 = ref<SelectProps["options"]>([
  {
    value: "lucy",
    label: "Lucy",
  },
]);

const handleChange = () => {
  console.log("view handleChange");
};
// const viewDetails = () => {
//   console.log("view details");
// };

// const handleSearch = (values: any) => {
//   console.log("Search values:", values);
// };

const chartOption = ref<any>({
  // // 提示框配置
  tooltip: {
    trigger: "axis", // 坐标轴触发
    axisPointer: {
      type: "shadow", // 阴影指示器
    },
    textStyle: {
      // color: "#fff",
      fontSize: 25,
    },
    formatter: function (params: any) {
      // 自定义提示框格式
      let result = params[0].name + "<br/>";
      params.forEach((item: any) => {
        result += item.marker + item.seriesName + ": " + item.value + "<br/>";
      });
      return result;
    },
  },
  // // 图例配置
  // legend: {
  //   // data: [], // 图例数据，从API获取
  //   // top: "0%", // 图例位置
  //   itemHeight: 20,
  //   itemWidth: 30,
  //   textStyle: {
  //     fontSize: 20,
  //     color: "#fff",
  //   },
  // },
  // // 图表网格配置
  grid: {
    left: "3%", // 左边距
    right: "4%", // 右边距
    bottom: "3%", // 下边距
    top: "15%", // 上边距，为图例留空间
    containLabel: true, // 包含坐标轴标签
  },
  // // X轴配置
  xAxis: {
    type: "category", // 类目轴
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
      // 标签格式化函数，处理过长的标签
      formatter: function (value: string) {
        if (value.length > 8) {
          return value.substring(0, 5) + "..."; // 超过5个字符显示省略号
        }
        return value;
      },
    },
  },
  dataset: {
    source: [
      ["product", "办公用水", "冷却用水", "其他"],
      ["1点", 43.3, 85.8, 93.7],
      ["2点", 83.1, 73.4, 55.1],
      ["3点", 86.4, 65.2, 82.5],
      ["5点", 72.4, 53.9, 39.1],
      ["6点", 72.4, 53.9, 39.1],
      ["7点", 72.4, 53.9, 39.1],
      ["8点", 72.4, 53.9, 39.1],
      ["9点", 72.4, 53.9, 39.1],
    ],
    // source: [
    //   ["product", "2015", "2016", "2017"],
    //   ["Matcha Latte", 43.3, 85.8, 93.7],
    //   ["Milk Tea", 83.1, 73.4, 55.1],
    //   ["Cheese Cocoa", 86.4, 65.2, 82.5],
    //   ["Walnut Brownie", 72.4, 53.9, 39.1],
    // ],
  },

  yAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      itemStyle: {
        borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
      },
      barWidth: "13%", // 柱子宽度，分组显示时需要调小
      type: "bar",
    },
    {
      itemStyle: {
        borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
      },
      barWidth: "13%",
      type: "bar",
    },
    {
      itemStyle: {
        borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
      },
      barWidth: "13%",
      type: "bar",
    },
  ],
});
</script>

<style lang="less" scoped>
.search-box {
  padding: 16px 16px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  color: #fff;
  span {
    margin-right: 10px;
  }
}
.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 535px;
  > div {
    height: 535px;
    flex: 1;
  }
}
</style>
