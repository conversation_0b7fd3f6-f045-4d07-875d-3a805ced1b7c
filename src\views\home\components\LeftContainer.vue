<template>
  <div class="left-container">
    <div class="top-box">
      <building-introduction />
      <cooperative-brands />
    </div>
    <security-system />
    <div class="bottom-box">
      <vehicle-statistics />
      <park-equipment />
    </div>
  </div>
</template>

<script setup lang="ts">
import BuildingIntroduction from "./BuildingIntroduction.vue";
import CooperativeBrands from "./CooperativeBrands.vue";
import SecuritySystem from "./SecuritySystem.vue";
import VehicleStatistics from "./VehicleStatistics.vue";
import ParkEquipment from "./ParkEquipment.vue";
</script>

<style scoped lang="less">
.left-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  height: 100%;
  overflow-y: auto;
  .top-box,
  .bottom-box {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    gap: 20px;
    flex: 1;
    flex-wrap: nowrap;
  }
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 26, 40, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(78, 237, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(78, 237, 255, 0.5);
    }
  }
}
</style>
