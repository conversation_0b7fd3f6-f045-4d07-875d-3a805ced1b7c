<template>
  <div class="parking-fee-container">
    <h2>停车费用管理</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <FeeCollection />
        <PaymentMethods />
      </div>
      <div class="right-panel">
        <RevenueAnalysis />
        <BillingRecords />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import FeeCollection from "@/components/parking/fee/FeeCollection.vue";
// import PaymentMethods from "@/components/parking/fee/PaymentMethods.vue";
// import RevenueAnalysis from "@/components/parking/fee/RevenueAnalysis.vue";
// import BillingRecords from "@/components/parking/fee/BillingRecords.vue";
//
</script>

<style scoped lang="less">
.parking-fee-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
