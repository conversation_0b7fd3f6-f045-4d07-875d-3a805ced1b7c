<template>
  <div class="elevator-maintenance-container">
    <h2>电梯维保管理</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <MaintenanceRecords />
        <InspectionSchedule />
      </div>
      <div class="right-panel">
        <RepairStatistics />
        <MaintenanceCompanies />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import MaintenanceRecords from "@/components/elevator/maintenance/MaintenanceRecords.vue";
// import InspectionSchedule from "@/components/elevator/maintenance/InspectionSchedule.vue";
// import RepairStatistics from "@/components/elevator/maintenance/RepairStatistics.vue";
// import MaintenanceCompanies from "@/components/elevator/maintenance/MaintenanceCompanies.vue";
</script>

<style scoped lang="less">
.elevator-maintenance-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
