// 能源管理相关类型定义

/** 通用API响应结构 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

/** 设备概览数据类型 */
export interface DeviceOverviewItem {
  id: string | number;
  name: string;
  type: string;
  status: "online" | "offline" | "fault" | "maintenance";
  location: string;
  power?: number; // 功率
  energy?: number; // 能耗
  efficiency?: number; // 效率
  lastUpdateTime: string;
  [key: string]: any; // 允许其他自定义字段
}

export interface DeviceOverviewResponse {
  total: number;
  online: number;
  offline: number;
  fault: number;
  maintenance: number;
  deviceList: DeviceOverviewItem[];
}

/** 设备状态列表数据类型 */
export interface DeviceStatusItem {
  id: string | number;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  status: "normal" | "warning" | "error" | "offline";
  statusText: string;
  location: string;
  building?: string;
  floor?: string;
  room?: string;
  parameters: {
    temperature?: number;
    humidity?: number;
    pressure?: number;
    voltage?: number;
    current?: number;
    power?: number;
    [key: string]: any;
  };
  alarmLevel?: "low" | "medium" | "high" | "critical";
  alarmMessage?: string;
  lastCheckTime: string;
  nextMaintenanceTime?: string;
}

export interface DeviceStatusListResponse {
  total: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
  statusSummary: {
    normal: number;
    warning: number;
    error: number;
    offline: number;
  };
  deviceList: DeviceStatusItem[];
}

/** 设备概览请求参数 */
export interface DeviceOverviewParams {
  buildingId?: string | number;
  floorId?: string | number;
  deviceType?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
}

/** 设备状态列表请求参数 */
export interface DeviceStatusListParams {
  page?: number;
  pageSize?: number;
  buildingId?: string | number;
  floorId?: string | number;
  deviceType?: string;
  status?: string;
  keyword?: string; // 搜索关键词
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  startTime?: string;
  endTime?: string;
}
