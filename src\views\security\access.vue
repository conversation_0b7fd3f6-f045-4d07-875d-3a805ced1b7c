<template>
  <div class="security-access-container">
    <h2>门禁考勤</h2>
    <!-- <div class="content-wrapper">
      <div class="left-panel">
        <AccessControlStatus />
        <AttendanceStatistics />
      </div>
      <div class="right-panel">
        <AccessRecords />
        <UnauthorizedAccess />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
// import AccessControlStatus from "@/components/security/access/AccessControlStatus.vue";
// import AttendanceStatistics from "@/components/security/access/AttendanceStatistics.vue";
// import AccessRecords from "@/components/security/access/AccessRecords.vue";
// import UnauthorizedAccess from "@/components/security/access/UnauthorizedAccess.vue";
</script>

<style scoped lang="less">
.security-access-container {
  padding: 20px;
  height: 100%;

  .content-wrapper {
    display: flex;
    height: calc(100% - 40px);

    .left-panel,
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
  }
}
</style>
