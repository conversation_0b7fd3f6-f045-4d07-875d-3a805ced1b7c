// 能源管理API使用示例

// import {
//   getDeviceOverview,
//   getDeviceStatusList,
//   getEnergyConsumption,
//   getDeviceAlarms,
// } from "@/api/energy";
// import type {
//   DeviceOverviewParams,
//   DeviceStatusListParams,
//   DeviceOverviewResponse,
//   DeviceStatusItem,
// } from "@/models/energy";
// import { ref } from "vue";

// /**
//  * 示例1: 获取设备概览
//  */
// export const fetchDeviceOverviewExample = async () => {
//   try {
//     // 基础调用 - 获取所有设备概览
//     const basicResponse = await getDeviceOverview();
//     console.log("设备概览数据:", basicResponse);

//     // 带参数调用 - 获取特定楼宇的设备概览
//     const params: DeviceOverviewParams = {
//       buildingId: "building_001",
//       floorId: "floor_001",
//       deviceType: "air_conditioner",
//       status: "online",
//       startTime: "2024-01-01 00:00:00",
//       endTime: "2024-12-31 23:59:59",
//     };

//     const detailedResponse = await getDeviceOverview(params);
//     console.log("特定条件设备概览:", detailedResponse);

//     // 处理响应数据
//     if (detailedResponse.success && detailedResponse.data) {
//       const { total, online, offline, fault, deviceList } = detailedResponse.data;
//       console.log(`总设备数: ${total}, 在线: ${online}, 离线: ${offline}, 故障: ${fault}`);
//       console.log("设备列表:", deviceList);
//     }
//   } catch (error) {
//     console.error("获取设备概览失败:", error);
//   }
// };

// /**
//  * 示例2: 获取设备状态列表
//  */
// export const fetchDeviceStatusListExample = async () => {
//   try {
//     // 分页查询设备状态列表
//     const params: DeviceStatusListParams = {
//       page: 1,
//       pageSize: 20,
//       buildingId: "building_001",
//       deviceType: "elevator",
//       status: "normal",
//       keyword: "电梯",
//       sortBy: "lastCheckTime",
//       sortOrder: "desc",
//     };

//     const response = await getDeviceStatusList(params);
//     console.log("设备状态列表:", response);

//     // 处理分页数据
//     if (response.success && response.data) {
//       const { total, currentPage, totalPages, statusSummary, deviceList } = response.data;

//       console.log(`第${currentPage}页，共${totalPages}页，总计${total}条记录`);
//       console.log("状态统计:", statusSummary);

//       // 遍历设备列表
//       deviceList.forEach((device:any) => {
//         console.log(`设备: ${device.deviceName}, 状态: ${device.status}`);
//         if (device.alarmLevel && device.alarmLevel !== "low") {
//           console.warn(`告警设备: ${device.deviceName}, 级别: ${device.alarmLevel}`);
//         }
//       });
//     }
//   } catch (error) {
//     console.error("获取设备状态列表失败:", error);
//   }
// };

// /**
//  * 示例3: 在Vue组件中使用
//  */
// export const useEnergyManagement = () => {
//   // 设备概览数据
//   const deviceOverview = ref<DeviceOverviewResponse | null>(null);
//   const deviceStatusList = ref<DeviceStatusItem[]>([]);
//   const loading = ref(false);

//   // 加载设备概览
//   const loadDeviceOverview = async (params?: DeviceOverviewParams) => {
//     loading.value = true;
//     try {
//       const response = await getDeviceOverview(params);
//       if (response.success) {
//         deviceOverview.value = response.data;
//       }
//     } catch (error) {
//       console.error("加载设备概览失败:", error);
//     } finally {
//       loading.value = false;
//     }
//   };

//   // 加载设备状态列表
//   const loadDeviceStatusList = async (params?: DeviceStatusListParams) => {
//     loading.value = true;
//     try {
//       const response = await getDeviceStatusList(params);
//       if (response.success) {
//         deviceStatusList.value = response.data.deviceList;
//       }
//     } catch (error) {
//       console.error("加载设备状态列表失败:", error);
//     } finally {
//       loading.value = false;
//     }
//   };

//   return {
//     deviceOverview,
//     deviceStatusList,
//     loading,
//     loadDeviceOverview,
//     loadDeviceStatusList,
//   };
// };

// /**
//  * 示例4: 错误处理和重试机制
//  */
// export const fetchWithRetry = async (apiCall: () => Promise<any>, maxRetries = 3) => {
//   let lastError;

//   for (let i = 0; i < maxRetries; i++) {
//     try {
//       const result = await apiCall();
//       return result;
//     } catch (error) {
//       lastError = error;
//       console.warn(`API调用失败，第${i + 1}次重试:`, error);

//       // 等待一段时间后重试
//       if (i < maxRetries - 1) {
//         await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
//       }
//     }
//   }

//   throw lastError;
// };

// // 使用重试机制的示例
// export const fetchDeviceOverviewWithRetry = () => {
//   return fetchWithRetry(() => getDeviceOverview());
// };
