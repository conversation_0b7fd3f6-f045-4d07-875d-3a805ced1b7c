import { EnergyRequest } from "@/utils/request";
import api from "@/utils/request";
import type {
  ApiResponse,
  DeviceOverviewItem,
  DeviceStatusListParams,
  DeviceStatusListResponse,
} from "@/models/energy";

/**
 * 获取设备概览
 * @param params 查询参数
 * @returns Promise<DeviceOverviewResponse>
 */
export const getDeviceOverview = () =>
  api.post<ApiResponse<DeviceOverviewItem[]>>("/shibomameng/energyManagement/equipment_Overview");

/**
 * 获取设备状态列表
 * @param params 查询参数
 * @returns Promise<DeviceStatusListResponse>
 */
export const getDeviceStatusList = (params?: DeviceStatusListParams) =>
  EnergyRequest.post<DeviceStatusListResponse>(
    "shibomameng/energyManagement/equipment_statuslist",
    params || {},
  );

// // 其他能源管理相关接口可以在这里继续添加

// /**
//  * 获取能耗统计数据
//  * @param params 查询参数
//  */
// export const getEnergyConsumption = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>("/shibomameng/energyManagement/energy_consumption", params);

// /**
//  * 获取设备运行状态统计
//  * @param params 查询参数
//  */
// export const getDeviceRunningStats = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/device_running_stats",
//     params,
//   );

// /**
//  * 获取能效分析数据
//  * @param params 查询参数
//  */
// export const getEnergyEfficiencyAnalysis = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/energy_efficiency_analysis",
//     params,
//   );

// /**
//  * 获取设备告警信息
//  * @param params 查询参数
//  */
// export const getDeviceAlarms = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>("/shibomameng/energyManagement/device_alarms", params);

// /**
//  * 获取设备维护记录
//  * @param params 查询参数
//  */
// export const getDeviceMaintenanceRecords = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/device_maintenance_records",
//     params,
//   );

// /**
//  * 更新设备状态
//  * @param params 更新参数
//  */
// export const updateDeviceStatus = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/update_device_status",
//     params,
//   );

// /**
//  * 添加设备维护记录
//  * @param params 维护记录参数
//  */
// export const addMaintenanceRecord = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/add_maintenance_record",
//     params,
//   );

// /**
//  * 获取实时能耗数据
//  * @param params 查询参数
//  */
// export const getRealTimeEnergyData = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/realtime_energy_data",
//     params,
//   );
