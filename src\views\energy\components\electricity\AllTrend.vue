<template>
  <!-- 总体用水趋势 -->
  <page-card :title="'总体用电趋势'">
    <div class="electricity-all-trend">
      <div class="search-box">
        <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
          <a-radio-button value="day">今日</a-radio-button>
          <a-radio-button value="month">本月</a-radio-button>
          <a-radio-button value="year">本年</a-radio-button>
        </a-radio-group>
      </div>
      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref } from "vue";

// const toDetails = (i: any) => {
//   console.log("%c Line:99 🍋 i", "color:#f5ce50", i);
// };
// // 表单相关
// const searchFields = [
//   {
//     type: "radio",
//     name: "type",
//     label: "",
//     options: [
//       { label: "今日", value: "day" },
//       { label: "本月", value: "month" },
//       { label: "本年", value: "year" },
//     ],
//   },
// ];

// const initialValues = ref({
//   type: "",
// });

// const searchResults = ref({});

// const handleSearch = (values: any) => {
//   console.log("Search values:", values);
//   // searchResults.value = values;
// };

// const handleReset = () => {
//   console.log("Form reset");
//   // searchResults.value = {};
// };

const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "3%",
    right: "4%",
    bottom: "10%",
    top: "10%",
    containLabel: true,
  },
  // 图例配置
  legend: {
    data: ["系统1", "系统2"], // 图例数据，从API获取
    icon: "roundRect",
    bottom: "0%", // 图例位置
    height: "20",
    textStyle: {
      fontSize: 20,
      color: "#fff",
      align: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    textStyle: {
      fontSize: 20,
      color: "#fff",
    },
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
    },
    // splitLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(255, 255, 255, 0.1)",
    //     type: "dashed",
    //   },
    // },
  },
  yAxis: {
    type: "value",
    name: "单位：kWh",
    nameGap: 30,
    nameTextStyle: {
      fontSize: 20,
      color: "#FFFFFF",
    },
    // min: 0,
    // max: 140,
    // interval: 20,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: 20,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "系统1",
      type: "line",
      smooth: true,
      symbol: "circle",
      symbolSize: 12,
      itemStyle: {
        color: "#00a0e9",
      },
      lineStyle: {
        width: 3,
        color: "#00a0e9",
      },
      data: [8, 22, 28, 28, 30, 36, 50, 62, 68, 84, 74, 38],
    },
    {
      name: "系统2",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      symbolSize: 12,
      itemStyle: {
        color: "#ff9f00",
      },
      lineStyle: {
        width: 3,
        color: "#ff9f00",
      },
      data: [105, 92, 84, 85, 85, 78, 62, 50, 45, 30, 38, 75],
    },
  ],
});
const timeRange = ref("year");
</script>

<style lang="less" scoped>
.chart-container {
  height: 510px;
}
.search-box {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  .name,
  .time {
    margin-right: 30px;
    :deep .ant-radio-button-wrapper {
      font-size: 18px;
    }
  }
}
</style>
