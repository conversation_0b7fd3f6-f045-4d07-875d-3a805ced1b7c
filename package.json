{"name": "janke-transport-digital-twin-platform", "version": "0.1.0", "private": true, "scripts": {"test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "dev-build": "vue-cli-service build --mode development", "dev-serve": "vue-cli-service serve --mode development", "prod-build": "vue-cli-service build --mode production", "prod-serve": "vue-cli-service serve --mode production", "prod-analyse": "vue-cli-service build --mode production --report"}, "dependencies": {"@babel/plugin-transform-typescript": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "animate.css": "^4.1.1", "ant-design-vue": "^3.2.15", "axios": "^1.2.0", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.8.3", "echarts": "^5.4.0", "less": "^4.1.3", "less-loader": "^11.1.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "postcss-pxtorem": "^6.1.0", "qweather-icons": "^1.3.1", "vue": "^3.2.13", "vue-router": "^4.0.3", "vue3-carousel-3d": "^1.0.4", "vuex": "^4.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-spread": "^7.27.1", "@types/lodash": "^4.14.191", "@types/video.js": "^7.3.50", "@types/vue3-carousel-3d": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "prettier": "^2.4.1", "typescript": "~4.5.5", "video.js": "^7.21.4"}}