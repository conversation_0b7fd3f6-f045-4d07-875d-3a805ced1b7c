<template>
  <PageLayout>
    <template v-slot:leftcontainer>
      <!-- 设备概览 -->
      <DeviceOverview />
      <!-- 绿色低碳系统 -->
      <GreenLowCarbonSystem />
      <!-- 故障报警 -->
      <FaultAlarm />
    </template>
    <template v-slot:rightcontainer>
      <!-- 设备状态 -->
      <DeviceStatus />
      <!-- 历史趋势 -->
      <HistoricalTrend />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import DeviceOverview from "@/views/iot-facilities/components/water-supply/DeviceOverview.vue";
import GreenLowCarbonSystem from "@/views/iot-facilities/components/water-supply/GreenLowCarbonSystem.vue";
import FaultAlarm from "@/views/iot-facilities/components/water-supply/FaultAlarm.vue";
import DeviceStatus from "@/views/iot-facilities/components/water-supply/DeviceStatus.vue";
import HistoricalTrend from "@/views/iot-facilities/components/water-supply/HistoricalTrend.vue";
</script>

<style scoped lang="less"></style>
