<template>
  <section class="app-main">
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </section>
</template>
<script lang="ts" setup>
import { provide, inject, onMounted, ref, nextTick } from "vue";
import { useRouter } from "vue-router";

const bus = inject("bus");
const router = useRouter();
const pageShow = ref(false);
provide("pageShow", pageShow);
const timeout = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};
onMounted(async () => {
  await timeout(500);
  pageShow.value = true;
  bus.on("routeChange", async (routeName: string) => {
    pageShow.value = false;
    await timeout(500);
    router.replace({ path: routeName });
    nextTick(async () => {
      await timeout(200);
      pageShow.value = true;
    });
  });
});
</script>
