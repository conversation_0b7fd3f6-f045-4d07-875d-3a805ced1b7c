<template>
  <PageCard :title="'用水结构'">
    <div class="search-box">
      <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
        <a-radio-button value="day">今日</a-radio-button>
        <a-radio-button value="month">本月</a-radio-button>
        <a-radio-button value="year">本年</a-radio-button>
      </a-radio-group>
    </div>

    <div class="chart-container">
      <common-chart :echart-obj="chartOption" />
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref } from "vue";
// import * as echarts from "echarts";

const timeRange = ref("year");

// 用水结构
const chartOption = ref({
  // tooltip: {
  //   trigger: "item",
  // },
  legend: {
    type: "scroll",
    orient: "vertical",
    right: "5%",
    top: "10%",
    itemHeight: 20,
    itemWidth: 30,
    textStyle: {
      color: "#fff",
      fontSize: 20,
    },
  },
  tooltip: {
    trigger: "item",
    formatter: "{b}: {c}%",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
      fontSize: 25,
    },
  },

  series: [
    {
      name: "Access From",
      type: "pie",
      radius: ["40%", "80%"],

      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      itemStyle: {
        // borderRadius: 10,
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 3,
      },
      label: {
        show: true,
        position: "outside",
        formatter: "{b}\n{c}%",
        color: "#fff",
        fontSize: 20,
        fontWeight: "bold",
      },
      labelLine: {
        // 标签的视觉引导线配置
        show: true,
        length: 15,
        length2: 20,
        minTurnAngle: 90,
        lineStyle: {
          width: 3,
          color: "#fff",
        },
      },
      data: [
        { value: 10, name: "公共用水" },
        { value: 20, name: "办公用水" },
        { value: 30, name: "锅炉补水" },
        { value: 15, name: "冷却塔补水" },
        { value: 25, name: "其他" },
      ],
      emphasis: {
        scaleSize: 20, // 高亮后扇区的放大尺寸
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
  ],
});
</script>

<style lang="less" scoped>
.search-box {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 425px;
  > div {
    height: 425px;
    flex: 1;
  }
}
</style>
