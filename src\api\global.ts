import API from "@/utils/request";
import { weatherType, weatherListRes, airRes } from "@/models/global";
const key = "37a1fd85b9be4405807141f39aae4d1e"; // 和风天气appid  修改
const location = 101020100; // 和风城市编码，当前上海

export const getWeather = () =>
  API.get<weatherType>("https://devapi.qweather.com/v7/weather/now", {
    params: {
      key,
      location,
      lang: "zh",
      unit: "m",
    },
  });
export const getWeekWeather = () =>
  API.get<weatherListRes>("https://devapi.qweather.com/v7/weather/7d", {
    params: {
      key,
      location,
      lang: "zh",
      unit: "m",
    },
  });
export const getNowAir = () =>
  API.get<airRes>("https://devapi.qweather.com/v7/air/now", {
    params: {
      key,
      location,
      lang: "zh",
      unit: "m",
    },
  });
